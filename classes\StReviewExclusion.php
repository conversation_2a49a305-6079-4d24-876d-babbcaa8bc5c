<?php
/**
 * Review Exclusion Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewExclusion extends ObjectModel
{
    /** @var string Type (email, domain, customer_group, product) */
    public $type;

    /** @var string Value */
    public $value;

    /** @var bool Active status */
    public $active;

    /** @var string Date add */
    public $date_add;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_exclusions',
        'primary' => 'id_exclusion',
        'fields' => [
            'type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['email', 'domain', 'customer_group', 'product']],
            'value' => ['type' => self::TYPE_STRING, 'validate' => 'isString', 'required' => true, 'size' => 255],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => true],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get all exclusions by type
     *
     * @param string $type
     * @param bool $active_only
     * @return array
     */
    public static function getExclusionsByType($type, $active_only = true)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('st_review_exclusions');
        $sql->where('type = "' . pSQL($type) . '"');
        
        if ($active_only) {
            $sql->where('active = 1');
        }
        
        $sql->orderBy('date_add DESC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Check if customer is excluded
     *
     * @param int $id_customer
     * @return bool
     */
    public static function isCustomerExcluded($id_customer)
    {
        $customer = new Customer($id_customer);
        if (!Validate::isLoadedObject($customer)) {
            return true;
        }

        // Check email exclusions
        if (self::isEmailExcluded($customer->email)) {
            return true;
        }

        // Check domain exclusions
        if (self::isDomainExcluded($customer->email)) {
            return true;
        }

        // Check customer group exclusions
        if (self::isCustomerGroupExcluded($customer->id_default_group)) {
            return true;
        }

        // Check if customer has opted out of newsletters
        if (!$customer->newsletter) {
            return true;
        }

        return false;
    }

    /**
     * Check if email is excluded
     *
     * @param string $email
     * @return bool
     */
    public static function isEmailExcluded($email)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_exclusions');
        $sql->where('type = "email"');
        $sql->where('value = "' . pSQL($email) . '"');
        $sql->where('active = 1');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Check if domain is excluded
     *
     * @param string $email
     * @return bool
     */
    public static function isDomainExcluded($email)
    {
        $domain = substr(strrchr($email, "@"), 1);
        
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_exclusions');
        $sql->where('type = "domain"');
        $sql->where('value = "' . pSQL($domain) . '"');
        $sql->where('active = 1');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Check if customer group is excluded
     *
     * @param int $id_group
     * @return bool
     */
    public static function isCustomerGroupExcluded($id_group)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_exclusions');
        $sql->where('type = "customer_group"');
        $sql->where('value = "' . (int)$id_group . '"');
        $sql->where('active = 1');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Check if product is excluded
     *
     * @param int $id_product
     * @return bool
     */
    public static function isProductExcluded($id_product)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_exclusions');
        $sql->where('type = "product"');
        $sql->where('value = "' . (int)$id_product . '"');
        $sql->where('active = 1');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Add email exclusion
     *
     * @param string $email
     * @return bool
     */
    public static function addEmailExclusion($email)
    {
        if (self::isEmailExcluded($email)) {
            return true; // Already excluded
        }

        $exclusion = new StReviewExclusion();
        $exclusion->type = 'email';
        $exclusion->value = $email;
        $exclusion->active = 1;

        return $exclusion->add();
    }

    /**
     * Add domain exclusion
     *
     * @param string $domain
     * @return bool
     */
    public static function addDomainExclusion($domain)
    {
        // Remove @ if present
        $domain = ltrim($domain, '@');

        if (self::isDomainExcluded('test@' . $domain)) {
            return true; // Already excluded
        }

        $exclusion = new StReviewExclusion();
        $exclusion->type = 'domain';
        $exclusion->value = $domain;
        $exclusion->active = 1;

        return $exclusion->add();
    }

    /**
     * Add customer group exclusion
     *
     * @param int $id_group
     * @return bool
     */
    public static function addCustomerGroupExclusion($id_group)
    {
        if (self::isCustomerGroupExcluded($id_group)) {
            return true; // Already excluded
        }

        $exclusion = new StReviewExclusion();
        $exclusion->type = 'customer_group';
        $exclusion->value = (string)$id_group;
        $exclusion->active = 1;

        return $exclusion->add();
    }

    /**
     * Add product exclusion
     *
     * @param int $id_product
     * @return bool
     */
    public static function addProductExclusion($id_product)
    {
        if (self::isProductExcluded($id_product)) {
            return true; // Already excluded
        }

        $exclusion = new StReviewExclusion();
        $exclusion->type = 'product';
        $exclusion->value = (string)$id_product;
        $exclusion->active = 1;

        return $exclusion->add();
    }

    /**
     * Remove exclusion
     *
     * @param string $type
     * @param string $value
     * @return bool
     */
    public static function removeExclusion($type, $value)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_exclusions` 
                WHERE `type` = "' . pSQL($type) . '" 
                AND `value` = "' . pSQL($value) . '"';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Toggle exclusion status
     *
     * @param int $id_exclusion
     * @return bool
     */
    public static function toggleStatusExclusion($id_exclusion)
    {
        $exclusion = new StReviewExclusion($id_exclusion);
        if (!Validate::isLoadedObject($exclusion)) {
            return false;
        }

        $exclusion->active = !$exclusion->active;
        return $exclusion->update();
    }

    /**
     * Get exclusion statistics
     *
     * @return array
     */
    public static function getExclusionStats()
    {
        $sql = new DbQuery();
        $sql->select('
            type,
            COUNT(*) as total,
            SUM(CASE WHEN active = 1 THEN 1 ELSE 0 END) as active_count
        ');
        $sql->from('st_review_exclusions');
        $sql->groupBy('type');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        $stats = [
            'email' => ['total' => 0, 'active' => 0],
            'domain' => ['total' => 0, 'active' => 0],
            'customer_group' => ['total' => 0, 'active' => 0],
            'product' => ['total' => 0, 'active' => 0]
        ];

        foreach ($results as $result) {
            $stats[$result['type']] = [
                'total' => (int)$result['total'],
                'active' => (int)$result['active_count']
            ];
        }

        return $stats;
    }

    /**
     * Import exclusions from CSV
     *
     * @param string $csv_content
     * @param string $type
     * @return array
     */
    public static function importFromCSV($csv_content, $type)
    {
        $results = [
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        $lines = explode("\n", $csv_content);
        
        foreach ($lines as $line_number => $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            $value = trim($line, '"');
            
            try {
                switch ($type) {
                    case 'email':
                        if (Validate::isEmail($value)) {
                            if (self::addEmailExclusion($value)) {
                                $results['imported']++;
                            } else {
                                $results['skipped']++;
                            }
                        } else {
                            $results['errors'][] = "Line " . ($line_number + 1) . ": Invalid email format";
                        }
                        break;
                        
                    case 'domain':
                        if (self::addDomainExclusion($value)) {
                            $results['imported']++;
                        } else {
                            $results['skipped']++;
                        }
                        break;
                        
                    case 'customer_group':
                        if (is_numeric($value) && $value > 0) {
                            if (self::addCustomerGroupExclusion((int)$value)) {
                                $results['imported']++;
                            } else {
                                $results['skipped']++;
                            }
                        } else {
                            $results['errors'][] = "Line " . ($line_number + 1) . ": Invalid customer group ID";
                        }
                        break;
                        
                    case 'product':
                        if (is_numeric($value) && $value > 0) {
                            if (self::addProductExclusion((int)$value)) {
                                $results['imported']++;
                            } else {
                                $results['skipped']++;
                            }
                        } else {
                            $results['errors'][] = "Line " . ($line_number + 1) . ": Invalid product ID";
                        }
                        break;
                }
            } catch (Exception $e) {
                $results['errors'][] = "Line " . ($line_number + 1) . ": " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Export exclusions to CSV
     *
     * @param string $type
     * @return string
     */
    public static function exportToCSV($type = null)
    {
        $sql = new DbQuery();
        $sql->select('type, value, active, date_add');
        $sql->from('st_review_exclusions');
        
        if ($type) {
            $sql->where('type = "' . pSQL($type) . '"');
        }
        
        $sql->orderBy('type ASC, date_add DESC');

        $exclusions = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        $csv = "Type,Value,Active,Date Added\n";
        
        foreach ($exclusions as $exclusion) {
            $csv .= sprintf(
                '"%s","%s","%s","%s"' . "\n",
                $exclusion['type'],
                $exclusion['value'],
                $exclusion['active'] ? 'Yes' : 'No',
                $exclusion['date_add']
            );
        }

        return $csv;
    }

    /**
     * Clean inactive exclusions
     *
     * @param int $days
     * @return bool
     */
    public static function cleanInactiveExclusions($days = 365)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_exclusions` 
                WHERE `active` = 0 
                AND `date_add` < DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)';

        return Db::getInstance()->execute($sql);
    }
}
