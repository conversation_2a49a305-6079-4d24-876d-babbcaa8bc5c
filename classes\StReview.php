<?php
/**
 * Review Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReview extends ObjectModel
{
    /** @var int Customer ID */
    public $id_customer;

    /** @var int Product ID */
    public $id_product;

    /** @var int Order ID */
    public $id_order;

    /** @var string Type (product, store) */
    public $type;

    /** @var string Title */
    public $title;

    /** @var string Content */
    public $content;

    /** @var float Overall rating */
    public $overall_rating;

    /** @var string Status (pending, approved, rejected) */
    public $status;

    /** @var bool Verified purchase */
    public $verified_purchase;

    /** @var int Helpful yes votes */
    public $helpful_yes;

    /** @var int Helpful no votes */
    public $helpful_no;

    /** @var string Date add */
    public $date_add;

    /** @var string Date update */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_reviews',
        'primary' => 'id_review',
        'fields' => [
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_product' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'id_order' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['product', 'store']],
            'title' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'content' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml', 'required' => true],
            'overall_rating' => ['type' => self::TYPE_FLOAT, 'validate' => 'isFloat', 'required' => true],
            'status' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['pending', 'approved', 'rejected']],
            'verified_purchase' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'helpful_yes' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'helpful_no' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get reviews for a product
     *
     * @param int $id_product
     * @param string $status
     * @param int $limit
     * @param int $offset
     * @param string $order_by
     * @param string $order_way
     * @return array
     */
    public static function getProductReviews($id_product, $status = 'approved', $limit = 10, $offset = 0, $order_by = 'date_add', $order_way = 'DESC')
    {
        $sql = new DbQuery();
        $sql->select('r.*, c.firstname, c.lastname, c.email');
        $sql->from('st_reviews', 'r');
        $sql->leftJoin('customer', 'c', 'r.id_customer = c.id_customer');
        $sql->where('r.id_product = ' . (int)$id_product);
        $sql->where('r.type = "product"');
        
        if ($status) {
            $sql->where('r.status = "' . pSQL($status) . '"');
        }
        
        $sql->orderBy(pSQL($order_by) . ' ' . pSQL($order_way));
        $sql->limit((int)$limit, (int)$offset);

        $reviews = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        // Get ratings for each review
        foreach ($reviews as &$review) {
            $review['ratings'] = StReviewRating::getRatingsByReview($review['id_review']);
            $review['reply'] = StReviewReply::getReplyByReview($review['id_review']);
        }
        
        return $reviews;
    }

    /**
     * Get store reviews
     *
     * @param string $status
     * @param int $limit
     * @param int $offset
     * @param string $order_by
     * @param string $order_way
     * @return array
     */
    public static function getStoreReviews($status = 'approved', $limit = 10, $offset = 0, $order_by = 'date_add', $order_way = 'DESC')
    {
        $sql = new DbQuery();
        $sql->select('r.*, c.firstname, c.lastname, c.email');
        $sql->from('st_reviews', 'r');
        $sql->leftJoin('customer', 'c', 'r.id_customer = c.id_customer');
        $sql->where('r.type = "store"');
        
        if ($status) {
            $sql->where('r.status = "' . pSQL($status) . '"');
        }
        
        $sql->orderBy(pSQL($order_by) . ' ' . pSQL($order_way));
        $sql->limit((int)$limit, (int)$offset);

        $reviews = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        // Get ratings for each review
        foreach ($reviews as &$review) {
            $review['ratings'] = StReviewRating::getRatingsByReview($review['id_review']);
            $review['reply'] = StReviewReply::getReplyByReview($review['id_review']);
        }
        
        return $reviews;
    }

    /**
     * Get customer reviews
     *
     * @param int $id_customer
     * @param string $type
     * @param string $status
     * @return array
     */
    public static function getCustomerReviews($id_customer, $type = null, $status = null)
    {
        $sql = new DbQuery();
        $sql->select('r.*, p.name as product_name');
        $sql->from('st_reviews', 'r');
        $sql->leftJoin('product_lang', 'p', 'r.id_product = p.id_product AND p.id_lang = ' . (int)Context::getContext()->language->id);
        $sql->where('r.id_customer = ' . (int)$id_customer);
        
        if ($type) {
            $sql->where('r.type = "' . pSQL($type) . '"');
        }
        
        if ($status) {
            $sql->where('r.status = "' . pSQL($status) . '"');
        }
        
        $sql->orderBy('r.date_add DESC');

        $reviews = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        // Get ratings for each review
        foreach ($reviews as &$review) {
            $review['ratings'] = StReviewRating::getRatingsByReview($review['id_review']);
            $review['reply'] = StReviewReply::getReplyByReview($review['id_review']);
        }
        
        return $reviews;
    }

    /**
     * Get product rating statistics
     *
     * @param int $id_product
     * @return array
     */
    public static function getProductRatingStats($id_product)
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_reviews,
            AVG(overall_rating) as average_rating,
            SUM(CASE WHEN overall_rating >= 5 THEN 1 ELSE 0 END) as five_stars,
            SUM(CASE WHEN overall_rating >= 4 AND overall_rating < 5 THEN 1 ELSE 0 END) as four_stars,
            SUM(CASE WHEN overall_rating >= 3 AND overall_rating < 4 THEN 1 ELSE 0 END) as three_stars,
            SUM(CASE WHEN overall_rating >= 2 AND overall_rating < 3 THEN 1 ELSE 0 END) as two_stars,
            SUM(CASE WHEN overall_rating < 2 THEN 1 ELSE 0 END) as one_star
        ');
        $sql->from('st_reviews');
        $sql->where('id_product = ' . (int)$id_product);
        $sql->where('type = "product"');
        $sql->where('status = "approved"');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Get store rating statistics
     *
     * @return array
     */
    public static function getStoreRatingStats()
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_reviews,
            AVG(overall_rating) as average_rating,
            SUM(CASE WHEN overall_rating >= 5 THEN 1 ELSE 0 END) as five_stars,
            SUM(CASE WHEN overall_rating >= 4 AND overall_rating < 5 THEN 1 ELSE 0 END) as four_stars,
            SUM(CASE WHEN overall_rating >= 3 AND overall_rating < 4 THEN 1 ELSE 0 END) as three_stars,
            SUM(CASE WHEN overall_rating >= 2 AND overall_rating < 3 THEN 1 ELSE 0 END) as two_stars,
            SUM(CASE WHEN overall_rating < 2 THEN 1 ELSE 0 END) as one_star
        ');
        $sql->from('st_reviews');
        $sql->where('type = "store"');
        $sql->where('status = "approved"');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Check if customer can review product
     *
     * @param int $id_customer
     * @param int $id_product
     * @return bool
     */
    public static function canCustomerReviewProduct($id_customer, $id_product)
    {
        // Check if customer has purchased the product
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('order_detail', 'od');
        $sql->innerJoin('orders', 'o', 'od.id_order = o.id_order');
        $sql->where('o.id_customer = ' . (int)$id_customer);
        $sql->where('od.product_id = ' . (int)$id_product);
        $sql->where('o.current_state IN (SELECT id_order_state FROM ' . _DB_PREFIX_ . 'order_state WHERE paid = 1)');

        $has_purchased = (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if (!$has_purchased) {
            return false;
        }

        // Check if customer has already reviewed this product
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_reviews');
        $sql->where('id_customer = ' . (int)$id_customer);
        $sql->where('id_product = ' . (int)$id_product);
        $sql->where('type = "product"');

        $has_reviewed = (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        return !$has_reviewed;
    }

    /**
     * Check if customer can review store
     *
     * @param int $id_customer
     * @return bool
     */
    public static function canCustomerReviewStore($id_customer)
    {
        // Check if customer has made at least one order
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('orders');
        $sql->where('id_customer = ' . (int)$id_customer);
        $sql->where('current_state IN (SELECT id_order_state FROM ' . _DB_PREFIX_ . 'order_state WHERE paid = 1)');

        $has_ordered = (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if (!$has_ordered) {
            return false;
        }

        // Check if customer has already reviewed the store
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_reviews');
        $sql->where('id_customer = ' . (int)$id_customer);
        $sql->where('type = "store"');

        $has_reviewed = (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        return !$has_reviewed;
    }

    /**
     * Add helpful vote
     *
     * @param bool $helpful
     * @return bool
     */
    public function addHelpfulVote($helpful = true)
    {
        if ($helpful) {
            $this->helpful_yes++;
        } else {
            $this->helpful_no++;
        }

        return $this->update();
    }

    /**
     * Calculate overall rating from individual criteria ratings
     *
     * @param array $ratings
     * @return float
     */
    public static function calculateOverallRating($ratings)
    {
        if (empty($ratings)) {
            return 0;
        }

        $total = 0;
        $count = 0;

        foreach ($ratings as $rating) {
            $total += (float)$rating;
            $count++;
        }

        return $count > 0 ? round($total / $count, 2) : 0;
    }

    /**
     * Approve review
     *
     * @return bool
     */
    public function approve()
    {
        $this->status = 'approved';
        return $this->update();
    }

    /**
     * Reject review
     *
     * @return bool
     */
    public function reject()
    {
        $this->status = 'rejected';
        return $this->update();
    }

    /**
     * Delete review and related data
     *
     * @return bool
     */
    public function delete()
    {
        // Delete related ratings
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_ratings` WHERE `id_review` = ' . (int)$this->id;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Delete related replies
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_replies` WHERE `id_review` = ' . (int)$this->id;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Delete related disputes
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_disputes` WHERE `id_review` = ' . (int)$this->id;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        return parent::delete();
    }
}
