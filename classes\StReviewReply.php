<?php
/**
 * Review Reply Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewReply extends ObjectModel
{
    /** @var int Review ID */
    public $id_review;

    /** @var int Employee ID */
    public $id_employee;

    /** @var string Content */
    public $content;

    /** @var string Date add */
    public $date_add;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_replies',
        'primary' => 'id_reply',
        'fields' => [
            'id_review' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_employee' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'content' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml', 'required' => true],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get reply by review ID
     *
     * @param int $id_review
     * @return array|false
     */
    public static function getReplyByReview($id_review)
    {
        $sql = new DbQuery();
        $sql->select('rr.*, e.firstname, e.lastname');
        $sql->from('st_review_replies', 'rr');
        $sql->leftJoin('employee', 'e', 'rr.id_employee = e.id_employee');
        $sql->where('rr.id_review = ' . (int)$id_review);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Check if review has reply
     *
     * @param int $id_review
     * @return bool
     */
    public static function hasReply($id_review)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_replies');
        $sql->where('id_review = ' . (int)$id_review);

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get all replies by employee
     *
     * @param int $id_employee
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public static function getRepliesByEmployee($id_employee, $limit = 20, $offset = 0)
    {
        $sql = new DbQuery();
        $sql->select('rr.*, r.title as review_title, r.type as review_type, c.firstname, c.lastname');
        $sql->from('st_review_replies', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->leftJoin('customer', 'c', 'r.id_customer = c.id_customer');
        $sql->where('rr.id_employee = ' . (int)$id_employee);
        $sql->orderBy('rr.date_add DESC');
        $sql->limit((int)$limit, (int)$offset);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get recent replies
     *
     * @param int $limit
     * @param int $days
     * @return array
     */
    public static function getRecentReplies($limit = 10, $days = 30)
    {
        $sql = new DbQuery();
        $sql->select('rr.*, r.title as review_title, r.type as review_type, e.firstname, e.lastname, c.firstname as customer_firstname, c.lastname as customer_lastname');
        $sql->from('st_review_replies', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->leftJoin('employee', 'e', 'rr.id_employee = e.id_employee');
        $sql->leftJoin('customer', 'c', 'r.id_customer = c.id_customer');
        $sql->where('rr.date_add >= DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)');
        $sql->orderBy('rr.date_add DESC');
        $sql->limit((int)$limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get reply statistics
     *
     * @param int $days
     * @return array
     */
    public static function getReplyStats($days = 30)
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_replies,
            COUNT(DISTINCT rr.id_employee) as active_employees,
            AVG(TIMESTAMPDIFF(HOUR, r.date_add, rr.date_add)) as avg_response_time_hours
        ');
        $sql->from('st_review_replies', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->where('rr.date_add >= DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Get reviews without replies
     *
     * @param string $type
     * @param int $days_old
     * @param int $limit
     * @return array
     */
    public static function getReviewsWithoutReplies($type = null, $days_old = 7, $limit = 20)
    {
        $sql = new DbQuery();
        $sql->select('r.*, c.firstname, c.lastname, c.email');
        $sql->from('st_reviews', 'r');
        $sql->leftJoin('customer', 'c', 'r.id_customer = c.id_customer');
        $sql->leftJoin('st_review_replies', 'rr', 'r.id_review = rr.id_review');
        $sql->where('rr.id_reply IS NULL');
        $sql->where('r.status = "approved"');
        $sql->where('r.date_add <= DATE_SUB(NOW(), INTERVAL ' . (int)$days_old . ' DAY)');

        if ($type) {
            $sql->where('r.type = "' . pSQL($type) . '"');
        }

        $sql->orderBy('r.date_add ASC');
        $sql->limit((int)$limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Send reply notification email to customer
     *
     * @param int $id_review
     * @return bool
     */
    public function sendReplyNotification($id_review)
    {
        $review = new StReview($id_review);
        if (!Validate::isLoadedObject($review)) {
            return false;
        }

        $customer = new Customer($review->id_customer);
        if (!Validate::isLoadedObject($customer)) {
            return false;
        }

        $employee = new Employee($this->id_employee);
        if (!Validate::isLoadedObject($employee)) {
            return false;
        }

        $template_vars = [
            '{customer_firstname}' => $customer->firstname,
            '{customer_lastname}' => $customer->lastname,
            '{review_title}' => $review->title,
            '{review_content}' => $review->content,
            '{reply_content}' => $this->content,
            '{employee_name}' => $employee->firstname . ' ' . $employee->lastname,
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{shop_url}' => Tools::getShopDomainSsl(true),
        ];

        try {
            Mail::Send(
                (int)Context::getContext()->language->id,
                'review_reply_notification',
                Mail::l('Reply to your review'),
                $template_vars,
                $customer->email,
                $customer->firstname . ' ' . $customer->lastname,
                null,
                null,
                null,
                null,
                dirname(__FILE__) . '/../mails/',
                false,
                (int)Context::getContext()->shop->id
            );
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get default reply templates
     *
     * @param int $id_lang
     * @return array
     */
    public static function getDefaultReplyTemplates($id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        // These could be stored in database or configuration
        $templates = [
            'thank_you' => [
                'name' => 'Thank You',
                'content' => 'Thank you for your review! We appreciate your feedback and are glad you had a positive experience with us.'
            ],
            'apology' => [
                'name' => 'Apology',
                'content' => 'We sincerely apologize for the experience you had. We take all feedback seriously and will work to improve. Please contact us directly so we can make this right.'
            ],
            'clarification' => [
                'name' => 'Request for Clarification',
                'content' => 'Thank you for your review. Could you please provide more details about your experience so we can better understand and address your concerns?'
            ],
            'resolution' => [
                'name' => 'Issue Resolution',
                'content' => 'Thank you for bringing this to our attention. We have investigated the issue and taken steps to resolve it. We hope you will give us another chance to serve you better.'
            ]
        ];

        return $templates;
    }

    /**
     * Auto-generate reply based on review rating
     *
     * @param StReview $review
     * @return string
     */
    public static function generateAutoReply($review)
    {
        $templates = self::getDefaultReplyTemplates();

        if ($review->overall_rating >= 4) {
            return $templates['thank_you']['content'];
        } elseif ($review->overall_rating >= 3) {
            return $templates['clarification']['content'];
        } else {
            return $templates['apology']['content'];
        }
    }

    /**
     * Update reply content
     *
     * @param string $content
     * @return bool
     */
    public function updateContent($content)
    {
        $this->content = $content;
        return $this->update();
    }

    /**
     * Delete reply
     *
     * @return bool
     */
    public function delete()
    {
        return parent::delete();
    }
}
