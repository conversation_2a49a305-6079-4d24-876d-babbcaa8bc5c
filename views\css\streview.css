/**
 * Advanced Product and Store Review Manager - Frontend Styles
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

/* ==========================================================================
   Product Reviews Styles
   ========================================================================== */

.product-reviews {
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Reviews Summary */
.reviews-summary {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.overall-rating {
    text-align: center;
}

.rating-number {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    line-height: 1;
}

.rating-stars {
    margin: 10px 0;
}

.rating-stars .star {
    font-size: 24px;
    margin: 0 2px;
}

.rating-count {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* Rating Breakdown */
.rating-breakdown {
    padding-left: 20px;
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 10px;
}

.star-label {
    min-width: 40px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 2px;
}

.progress {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.rating-bar .count {
    min-width: 30px;
    font-size: 0.8rem;
    color: #666;
    text-align: right;
}

/* Criteria Averages */
.criteria-averages {
    margin: 30px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.criteria-averages h4 {
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
}

.criteria-item {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 10px;
}

.criteria-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.criteria-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.criteria-rating .stars .star {
    font-size: 16px;
    margin: 0 1px;
}

.rating-value {
    font-weight: 600;
    color: #666;
    font-size: 0.9rem;
}

/* Write Review Section */
.write-review-section {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.btn-write-review {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-write-review:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Reviews List */
.reviews-list h4 {
    margin-bottom: 25px;
    color: #333;
    font-weight: 600;
}

.review-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}

.review-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reviewer-name {
    color: #333;
    font-weight: 600;
}

.verified-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    padding: 2px 8px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 12px;
}

.verified-badge .material-icons {
    font-size: 14px;
}

.review-date {
    color: #666;
    font-size: 0.9rem;
}

/* Review Rating */
.review-rating {
    margin-bottom: 15px;
}

.overall-stars {
    margin-bottom: 10px;
}

.overall-stars .star {
    font-size: 20px;
    margin: 0 1px;
}

.individual-ratings {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.rating-criteria {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
}

.criteria-stars .star.mini {
    font-size: 14px;
    margin: 0;
}

/* Review Content */
.review-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.review-text {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Merchant Reply */
.merchant-reply {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 15px;
    margin-top: 15px;
    border-radius: 0 8px 8px 0;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.reply-header strong {
    color: #667eea;
}

.reply-date {
    color: #666;
    font-size: 0.8rem;
}

.reply-content {
    color: #555;
    line-height: 1.5;
}

/* Review Actions */
.review-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    flex-wrap: wrap;
    gap: 10px;
}

.helpful-votes {
    display: flex;
    align-items: center;
    gap: 10px;
}

.helpful-label {
    font-size: 0.9rem;
    color: #666;
}

.helpful-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 15px;
}

.helpful-btn .material-icons {
    font-size: 16px;
}

.social-sharing {
    display: flex;
    gap: 5px;
}

.share-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.btn-facebook {
    background: #3b5998;
    border-color: #3b5998;
    color: white;
}

.btn-twitter {
    background: #1da1f2;
    border-color: #1da1f2;
    color: white;
}

.report-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    padding: 4px 8px;
    text-decoration: none;
}

.report-btn:hover {
    color: #dc3545 !important;
    text-decoration: none;
}

.report-btn .material-icons {
    font-size: 16px;
}

/* No Reviews */
.no-reviews {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 30px 0;
}

.no-reviews .material-icons {
    margin-bottom: 20px;
}

.no-reviews h4 {
    color: #666;
    margin-bottom: 10px;
}

/* Load More */
.load-more-section {
    margin: 30px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .reviews-summary .row {
        flex-direction: column;
    }
    
    .overall-rating {
        margin-bottom: 20px;
    }
    
    .rating-breakdown {
        padding-left: 0;
    }
    
    .individual-ratings {
        flex-direction: column;
        gap: 8px;
    }
    
    .review-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .helpful-votes {
        width: 100%;
        justify-content: center;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.review-item {
    animation: fadeInUp 0.5s ease-out;
}

/* Star Rating Hover Effects */
.star {
    transition: all 0.2s ease;
}

.star:hover {
    transform: scale(1.1);
}

.star.filled {
    filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}
