{**
 * Admin Review View Template
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 *}

<div class="panel">
  <div class="panel-heading">
    <i class="icon-star"></i>
    {l s='Review Details' mod='streviewmaneger'}
  </div>
  
  <div class="panel-body">
    <div class="row">
      <div class="col-md-8">
        {* Review Information *}
        <div class="form-group">
          <label class="control-label">{l s='Type' mod='streviewmaneger'}:</label>
          <span class="badge badge-{if $review->type == 'product'}info{else}success{/if}">
            {if $review->type == 'product'}{l s='Product Review' mod='streviewmaneger'}{else}{l s='Store Review' mod='streviewmaneger'}{/if}
          </span>
        </div>

        <div class="form-group">
          <label class="control-label">{l s='Title' mod='streviewmaneger'}:</label>
          <h4>{$review->title|escape:'html':'UTF-8'}</h4>
        </div>

        <div class="form-group">
          <label class="control-label">{l s='Content' mod='streviewmaneger'}:</label>
          <div class="well">
            {$review->content|nl2br}
          </div>
        </div>

        {* Customer Information *}
        <div class="form-group">
          <label class="control-label">{l s='Customer' mod='streviewmaneger'}:</label>
          <p>
            <strong>{$customer->firstname} {$customer->lastname}</strong><br>
            <a href="mailto:{$customer->email}">{$customer->email}</a>
          </p>
        </div>

        {* Product Information *}
        {if $review->type == 'product' && $product}
          <div class="form-group">
            <label class="control-label">{l s='Product' mod='streviewmaneger'}:</label>
            <p>
              <strong>{$product->name}</strong><br>
              <small>ID: {$product->id}</small>
            </p>
          </div>
        {/if}

        {* Individual Ratings *}
        {if $ratings}
          <div class="form-group">
            <label class="control-label">{l s='Individual Ratings' mod='streviewmaneger'}:</label>
            <div class="ratings-display">
              {foreach $ratings as $rating}
                <div class="rating-item">
                  <span class="criteria-name">{$rating.criteria_name}:</span>
                  <span class="rating-stars">
                    {for $i=1 to 5}
                      {if $i <= $rating.rating}
                        <i class="icon-star" style="color: #FFD700;"></i>
                      {else}
                        <i class="icon-star-o" style="color: #ccc;"></i>
                      {/if}
                    {/for}
                  </span>
                  <span class="rating-value">({$rating.rating}/5)</span>
                </div>
              {/foreach}
            </div>
          </div>
        {/if}
      </div>

      <div class="col-md-4">
        {* Review Status *}
        <div class="panel panel-default">
          <div class="panel-heading">
            <h4 class="panel-title">{l s='Review Status' mod='streviewmaneger'}</h4>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label>{l s='Overall Rating' mod='streviewmaneger'}:</label>
              <div class="rating-display">
                {for $i=1 to 5}
                  {if $i <= $review->overall_rating}
                    <i class="icon-star" style="color: #FFD700; font-size: 18px;"></i>
                  {else}
                    <i class="icon-star-o" style="color: #ccc; font-size: 18px;"></i>
                  {/if}
                {/for}
                <span class="rating-number">({$review->overall_rating}/5)</span>
              </div>
            </div>

            <div class="form-group">
              <label>{l s='Status' mod='streviewmaneger'}:</label>
              <span class="badge badge-{if $review->status == 'approved'}success{elseif $review->status == 'pending'}warning{else}danger{/if}">
                {$review->status|ucfirst}
              </span>
            </div>

            <div class="form-group">
              <label>{l s='Verified Purchase' mod='streviewmaneger'}:</label>
              {if $review->verified_purchase}
                <span class="badge badge-success">
                  <i class="icon-check"></i> {l s='Verified' mod='streviewmaneger'}
                </span>
              {else}
                <span class="badge badge-default">
                  <i class="icon-remove"></i> {l s='Not Verified' mod='streviewmaneger'}
                </span>
              {/if}
            </div>

            <div class="form-group">
              <label>{l s='Date Added' mod='streviewmaneger'}:</label>
              <p>{$review->date_add|date_format:"%Y-%m-%d %H:%M"}</p>
            </div>

            <div class="form-group">
              <label>{l s='Helpful Votes' mod='streviewmaneger'}:</label>
              <p>
                <i class="icon-thumbs-up text-success"></i> {$review->helpful_yes} &nbsp;
                <i class="icon-thumbs-down text-danger"></i> {$review->helpful_no}
              </p>
            </div>
          </div>
        </div>

        {* Actions *}
        <div class="panel panel-default">
          <div class="panel-heading">
            <h4 class="panel-title">{l s='Actions' mod='streviewmaneger'}</h4>
          </div>
          <div class="panel-body">
            {if $review->status == 'pending'}
              <a href="{$current_index}&approvest_reviews&id_review={$review->id}&token={$token}" 
                 class="btn btn-success btn-block">
                <i class="icon-check"></i> {l s='Approve' mod='streviewmaneger'}
              </a>
              <a href="{$current_index}&rejectst_reviews&id_review={$review->id}&token={$token}" 
                 class="btn btn-danger btn-block">
                <i class="icon-remove"></i> {l s='Reject' mod='streviewmaneger'}
              </a>
            {elseif $review->status == 'approved'}
              <a href="{$current_index}&rejectst_reviews&id_review={$review->id}&token={$token}" 
                 class="btn btn-warning btn-block">
                <i class="icon-remove"></i> {l s='Reject' mod='streviewmaneger'}
              </a>
            {elseif $review->status == 'rejected'}
              <a href="{$current_index}&approvest_reviews&id_review={$review->id}&token={$token}" 
                 class="btn btn-success btn-block">
                <i class="icon-check"></i> {l s='Approve' mod='streviewmaneger'}
              </a>
            {/if}
            
            <a href="{$current_index}&updatest_reviews&id_review={$review->id}&token={$token}" 
               class="btn btn-default btn-block">
              <i class="icon-edit"></i> {l s='Edit' mod='streviewmaneger'}
            </a>
            
            <a href="{$current_index}&deletest_reviews&id_review={$review->id}&token={$token}" 
               class="btn btn-danger btn-block"
               onclick="return confirm('{l s='Are you sure you want to delete this review?' mod='streviewmaneger'}')">
              <i class="icon-trash"></i> {l s='Delete' mod='streviewmaneger'}
            </a>
          </div>
        </div>
      </div>
    </div>

    {* Reply Section *}
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h4 class="panel-title">
              <i class="icon-reply"></i> {l s='Merchant Reply' mod='streviewmaneger'}
            </h4>
          </div>
          <div class="panel-body">
            {if $reply}
              <div class="existing-reply">
                <div class="reply-meta">
                  <strong>{$reply.firstname} {$reply.lastname}</strong>
                  <small class="text-muted">- {$reply.date_add|date_format:"%Y-%m-%d %H:%M"}</small>
                </div>
                <div class="reply-content">
                  {$reply.content|nl2br}
                </div>
                <div class="reply-actions">
                  <button type="button" class="btn btn-sm btn-default" onclick="editReply()">
                    <i class="icon-edit"></i> {l s='Edit Reply' mod='streviewmaneger'}
                  </button>
                </div>
              </div>
            {else}
              <p class="text-muted">{l s='No reply yet.' mod='streviewmaneger'}</p>
            {/if}

            <div id="reply-form" style="{if $reply}display:none;{/if}">
              <form action="{$current_index}&replyst_reviews&id_review={$review->id}&token={$token}" method="post">
                <div class="form-group">
                  <label for="reply-content">{l s='Your Reply' mod='streviewmaneger'}:</label>
                  <textarea name="reply_content" 
                            id="reply-content" 
                            class="form-control" 
                            rows="4" 
                            placeholder="{l s='Write your reply to this review...' mod='streviewmaneger'}">{if $reply}{$reply.content|escape:'html':'UTF-8'}{/if}</textarea>
                </div>
                
                <div class="form-group">
                  <button type="submit" class="btn btn-primary">
                    <i class="icon-reply"></i> 
                    {if $reply}{l s='Update Reply' mod='streviewmaneger'}{else}{l s='Send Reply' mod='streviewmaneger'}{/if}
                  </button>
                  {if $reply}
                    <button type="button" class="btn btn-default" onclick="cancelEdit()">
                      {l s='Cancel' mod='streviewmaneger'}
                    </button>
                  {/if}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.rating-item {
  margin-bottom: 10px;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
}

.criteria-name {
  font-weight: bold;
  margin-right: 10px;
}

.rating-stars {
  margin-right: 10px;
}

.rating-value {
  color: #666;
  font-size: 0.9em;
}

.rating-display {
  margin-bottom: 10px;
}

.rating-number {
  margin-left: 10px;
  font-weight: bold;
  color: #333;
}

.existing-reply {
  background: #f0f8ff;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.reply-meta {
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.reply-content {
  margin-bottom: 10px;
  line-height: 1.5;
}

.reply-actions {
  text-align: right;
}
</style>

<script>
function editReply() {
  document.querySelector('.existing-reply').style.display = 'none';
  document.getElementById('reply-form').style.display = 'block';
}

function cancelEdit() {
  document.querySelector('.existing-reply').style.display = 'block';
  document.getElementById('reply-form').style.display = 'none';
}
</script>
