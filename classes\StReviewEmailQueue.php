<?php
/**
 * Review Email Queue Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewEmailQueue extends ObjectModel
{
    /** @var int Customer ID */
    public $id_customer;

    /** @var int Order ID */
    public $id_order;

    /** @var string Email type (first_request, reminder) */
    public $email_type;

    /** @var string Review type (product, store) */
    public $review_type;

    /** @var string Status (pending, sent, failed) */
    public $status;

    /** @var string Scheduled date */
    public $scheduled_date;

    /** @var string Sent date */
    public $sent_date;

    /** @var int Attempts */
    public $attempts;

    /** @var string Last error */
    public $last_error;

    /** @var string Date add */
    public $date_add;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_email_queue',
        'primary' => 'id_queue',
        'fields' => [
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_order' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'email_type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['first_request', 'reminder']],
            'review_type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['product', 'store']],
            'status' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['pending', 'sent', 'failed']],
            'scheduled_date' => ['type' => self::TYPE_DATE, 'validate' => 'isDate', 'required' => true],
            'sent_date' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'attempts' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'last_error' => ['type' => self::TYPE_STRING, 'validate' => 'isString'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Add email to queue
     *
     * @param int $id_customer
     * @param int $id_order
     * @param string $email_type
     * @param string $review_type
     * @param int $delay_days
     * @return bool
     */
    public static function addToQueue($id_customer, $id_order, $email_type = 'first_request', $review_type = 'product', $delay_days = 7)
    {
        // Check if customer is excluded
        if (StReviewExclusion::isCustomerExcluded($id_customer)) {
            return false;
        }

        // Check if email already exists in queue
        if (self::emailExists($id_customer, $id_order, $email_type, $review_type)) {
            return false;
        }

        $queue_item = new StReviewEmailQueue();
        $queue_item->id_customer = (int)$id_customer;
        $queue_item->id_order = (int)$id_order;
        $queue_item->email_type = $email_type;
        $queue_item->review_type = $review_type;
        $queue_item->status = 'pending';
        $queue_item->scheduled_date = date('Y-m-d H:i:s', strtotime('+' . (int)$delay_days . ' days'));
        $queue_item->attempts = 0;

        return $queue_item->add();
    }

    /**
     * Check if email already exists in queue
     *
     * @param int $id_customer
     * @param int $id_order
     * @param string $email_type
     * @param string $review_type
     * @return bool
     */
    public static function emailExists($id_customer, $id_order, $email_type, $review_type)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_email_queue');
        $sql->where('id_customer = ' . (int)$id_customer);
        $sql->where('id_order = ' . (int)$id_order);
        $sql->where('email_type = "' . pSQL($email_type) . '"');
        $sql->where('review_type = "' . pSQL($review_type) . '"');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get pending emails ready to send
     *
     * @param int $limit
     * @return array
     */
    public static function getPendingEmails($limit = 50)
    {
        $sql = new DbQuery();
        $sql->select('q.*, c.firstname, c.lastname, c.email, o.reference as order_reference');
        $sql->from('st_review_email_queue', 'q');
        $sql->innerJoin('customer', 'c', 'q.id_customer = c.id_customer');
        $sql->innerJoin('orders', 'o', 'q.id_order = o.id_order');
        $sql->where('q.status = "pending"');
        $sql->where('q.scheduled_date <= NOW()');
        $sql->where('q.attempts < 3'); // Max 3 attempts
        $sql->orderBy('q.scheduled_date ASC');
        $sql->limit((int)$limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Process email queue
     *
     * @param int $limit
     * @return array
     */
    public static function processQueue($limit = 50)
    {
        $emails = self::getPendingEmails($limit);
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($emails as $email_data) {
            $queue_item = new StReviewEmailQueue($email_data['id_queue']);
            
            if ($queue_item->sendEmail()) {
                $results['sent']++;
            } else {
                $results['failed']++;
                $results['errors'][] = $queue_item->last_error;
            }
        }

        return $results;
    }

    /**
     * Send email
     *
     * @return bool
     */
    public function sendEmail()
    {
        $this->attempts++;

        try {
            $customer = new Customer($this->id_customer);
            $order = new Order($this->id_order);

            if (!Validate::isLoadedObject($customer) || !Validate::isLoadedObject($order)) {
                $this->last_error = 'Invalid customer or order';
                $this->status = 'failed';
                $this->update();
                return false;
            }

            // Check if customer is excluded
            if (StReviewExclusion::isCustomerExcluded($this->id_customer)) {
                $this->last_error = 'Customer is excluded';
                $this->status = 'failed';
                $this->update();
                return false;
            }

            // Get order products for product reviews
            $products = [];
            if ($this->review_type === 'product') {
                $products = $order->getProducts();
            }

            // Generate review links
            $review_links = $this->generateReviewLinks($products);

            $template_vars = [
                '{customer_firstname}' => $customer->firstname,
                '{customer_lastname}' => $customer->lastname,
                '{order_reference}' => $order->reference,
                '{order_date}' => Tools::displayDate($order->date_add),
                '{review_links}' => $review_links,
                '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
                '{shop_url}' => Tools::getShopDomainSsl(true),
                '{unsubscribe_link}' => $this->generateUnsubscribeLink(),
            ];

            $template_name = $this->getTemplateName();
            $subject = $this->getEmailSubject();

            $sent = Mail::Send(
                (int)Context::getContext()->language->id,
                $template_name,
                $subject,
                $template_vars,
                $customer->email,
                $customer->firstname . ' ' . $customer->lastname,
                null,
                null,
                null,
                null,
                dirname(__FILE__) . '/../mails/',
                false,
                (int)Context::getContext()->shop->id
            );

            if ($sent) {
                $this->status = 'sent';
                $this->sent_date = date('Y-m-d H:i:s');
                $this->last_error = null;

                // Schedule reminder if this was first request
                if ($this->email_type === 'first_request' && Configuration::get('STREVIEW_REMINDER_ENABLED')) {
                    $reminder_delay = (int)Configuration::get('STREVIEW_REMINDER_DELAY_DAYS', 14);
                    self::addToQueue($this->id_customer, $this->id_order, 'reminder', $this->review_type, $reminder_delay);
                }
            } else {
                $this->last_error = 'Failed to send email';
                if ($this->attempts >= 3) {
                    $this->status = 'failed';
                }
            }

            $this->update();
            return $sent;

        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            if ($this->attempts >= 3) {
                $this->status = 'failed';
            }
            $this->update();
            return false;
        }
    }

    /**
     * Generate review links
     *
     * @param array $products
     * @return string
     */
    private function generateReviewLinks($products = [])
    {
        $links = '';
        $context = Context::getContext();

        if ($this->review_type === 'product' && !empty($products)) {
            foreach ($products as $product) {
                $product_link = $context->link->getProductLink($product['product_id']);
                $review_link = $product_link . '?action=review&token=' . $this->generateReviewToken($product['product_id']);
                $links .= '<p><a href="' . $review_link . '">' . $product['product_name'] . '</a></p>';
            }
        } else {
            // Store review link
            $review_link = $context->link->getPageLink('index') . '?action=store_review&token=' . $this->generateReviewToken();
            $links = '<p><a href="' . $review_link . '">Review our store</a></p>';
        }

        return $links;
    }

    /**
     * Generate review token
     *
     * @param int $id_product
     * @return string
     */
    private function generateReviewToken($id_product = null)
    {
        $data = $this->id_customer . '|' . $this->id_order . '|' . ($id_product ?: 'store');
        return Tools::encrypt($data);
    }

    /**
     * Generate unsubscribe link
     *
     * @return string
     */
    private function generateUnsubscribeLink()
    {
        $token = Tools::encrypt($this->id_customer . '|unsubscribe');
        return Context::getContext()->link->getPageLink('index') . '?action=unsubscribe&token=' . $token;
    }

    /**
     * Get template name
     *
     * @return string
     */
    private function getTemplateName()
    {
        $template_map = [
            'first_request' => [
                'product' => 'review_request_product',
                'store' => 'review_request_store'
            ],
            'reminder' => [
                'product' => 'review_reminder_product',
                'store' => 'review_reminder_store'
            ]
        ];

        return $template_map[$this->email_type][$this->review_type];
    }

    /**
     * Get email subject
     *
     * @return string
     */
    private function getEmailSubject()
    {
        $subject_map = [
            'first_request' => [
                'product' => 'Please review your recent purchase',
                'store' => 'How was your shopping experience?'
            ],
            'reminder' => [
                'product' => 'Reminder: Review your purchase',
                'store' => 'Reminder: Share your experience'
            ]
        ];

        return Mail::l($subject_map[$this->email_type][$this->review_type]);
    }

    /**
     * Get queue statistics
     *
     * @param int $days
     * @return array
     */
    public static function getQueueStats($days = 30)
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_emails,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_emails,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_emails,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_emails,
            AVG(attempts) as avg_attempts
        ');
        $sql->from('st_review_email_queue');
        $sql->where('date_add >= DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Clean old queue items
     *
     * @param int $days
     * @return bool
     */
    public static function cleanOldItems($days = 90)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_email_queue` 
                WHERE `date_add` < DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)
                AND `status` IN ("sent", "failed")';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Cancel pending emails for customer/order
     *
     * @param int $id_customer
     * @param int $id_order
     * @return bool
     */
    public static function cancelPendingEmails($id_customer, $id_order = null)
    {
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'st_review_email_queue` 
                SET `status` = "failed", `last_error` = "Cancelled"
                WHERE `id_customer` = ' . (int)$id_customer . '
                AND `status` = "pending"';

        if ($id_order) {
            $sql .= ' AND `id_order` = ' . (int)$id_order;
        }

        return Db::getInstance()->execute($sql);
    }
}
