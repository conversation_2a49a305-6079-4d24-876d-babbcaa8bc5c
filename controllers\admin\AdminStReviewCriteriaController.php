<?php
/**
 * Admin Review Criteria Controller
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStReviewCriteriaController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'st_review_criteria';
        $this->className = 'StReviewCriteria';
        $this->identifier = 'id_criteria';
        $this->bootstrap = true;
        $this->lang = true;

        parent::__construct();

        $this->fields_list = [
            'id_criteria' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'name' => [
                'title' => $this->l('Name'),
                'width' => 'auto',
                'filter_key' => 'b!name'
            ],
            'type' => [
                'title' => $this->l('Type'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'type' => 'select',
                'list' => [
                    'product' => $this->l('Product'),
                    'store' => $this->l('Store'),
                    'both' => $this->l('Both')
                ],
                'filter_key' => 'a!type'
            ],
            'position' => [
                'title' => $this->l('Position'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'position' => 'position'
            ],
            'active' => [
                'title' => $this->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ]
        ];

        $this->bulk_actions = [
            'enableSelection' => [
                'text' => $this->l('Enable selection'),
                'icon' => 'icon-power-off text-success'
            ],
            'disableSelection' => [
                'text' => $this->l('Disable selection'),
                'icon' => 'icon-power-off text-danger'
            ],
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            ]
        ];

        $this->actions = ['edit', 'delete'];
    }

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_criteria'] = [
                'href' => self::$currentIndex . '&addst_review_criteria&token=' . $this->token,
                'desc' => $this->l('Add new criteria'),
                'icon' => 'process-icon-new'
            ];
        }

        parent::initPageHeaderToolbar();
    }

    public function renderForm()
    {
        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Review Criteria'),
                'icon' => 'icon-star'
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'lang' => true,
                    'required' => true,
                    'size' => 50,
                    'desc' => $this->l('Name of the rating criteria')
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Type'),
                    'name' => 'type',
                    'required' => true,
                    'options' => [
                        'query' => [
                            ['id' => 'product', 'name' => $this->l('Product Reviews Only')],
                            ['id' => 'store', 'name' => $this->l('Store Reviews Only')],
                            ['id' => 'both', 'name' => $this->l('Both Product and Store Reviews')]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ],
                    'desc' => $this->l('Where this criteria will be used')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Position'),
                    'name' => 'position',
                    'class' => 'fixed-width-sm',
                    'desc' => $this->l('Position in the criteria list (lower numbers appear first)')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        ]
                    ]
                ]
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        if (!($obj = $this->loadObject(true))) {
            return;
        }

        // Set default position for new criteria
        if (!$obj->id) {
            $this->fields_value['position'] = StReviewCriteria::getNextPosition();
            $this->fields_value['active'] = 1;
        }

        return parent::renderForm();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitAddst_review_criteria')) {
            $id_criteria = (int)Tools::getValue('id_criteria');
            $criteria = new StReviewCriteria($id_criteria);

            $criteria->type = Tools::getValue('type');
            $criteria->active = (int)Tools::getValue('active');
            $criteria->position = (int)Tools::getValue('position');

            // Handle multilingual name
            $languages = Language::getLanguages(false);
            $criteria->name = [];
            foreach ($languages as $language) {
                $criteria->name[$language['id_lang']] = Tools::getValue('name_' . $language['id_lang']);
            }

            if ($id_criteria) {
                if ($criteria->update()) {
                    $this->confirmations[] = $this->l('Criteria updated successfully.');
                } else {
                    $this->errors[] = $this->l('Error occurred while updating criteria.');
                }
            } else {
                if ($criteria->add()) {
                    $this->confirmations[] = $this->l('Criteria added successfully.');
                } else {
                    $this->errors[] = $this->l('Error occurred while adding criteria.');
                }
            }
        }

        return parent::postProcess();
    }

    public function ajaxProcessUpdatePositions()
    {
        $way = (int)Tools::getValue('way');
        $id_criteria = (int)Tools::getValue('id');
        $positions = Tools::getValue($this->table);

        if (is_array($positions)) {
            foreach ($positions as $position => $value) {
                $pos = explode('_', $value);

                if (isset($pos[2]) && (int)$pos[2] === $id_criteria) {
                    if ($criteria = new StReviewCriteria((int)$pos[2])) {
                        if (isset($position) && $criteria->updatePosition($way, $position, $id_criteria)) {
                            echo 'ok position ' . (int)$position . ' for criteria ' . (int)$pos[2] . '\r\n';
                        } else {
                            echo '{"hasError" : true, "errors" : "Can not update criteria ' . (int)$id_criteria . ' to position ' . (int)$position . ' "}';
                        }
                    } else {
                        echo '{"hasError" : true, "errors" : "This criteria (' . (int)$id_criteria . ') can t be loaded"}';
                    }
                    break;
                }
            }
        }
    }

    public function processStatus()
    {
        $this->loadObject(true);
        if (!Validate::isLoadedObject($this->object)) {
            $this->errors[] = $this->l('An error occurred while updating the status.');
        } else {
            if ($this->object->toggleStatusExclusion()) {
                $this->confirmations[] = $this->l('The status has been updated successfully.');
            } else {
                $this->errors[] = $this->l('An error occurred while updating the status.');
            }
        }

        return $this->object;
    }

    public function processDelete()
    {
        $criteria = $this->loadObject();
        if (!Validate::isLoadedObject($criteria)) {
            $this->errors[] = $this->l('An error occurred while deleting the object.');
        } else {
            if ($criteria->delete()) {
                $this->confirmations[] = $this->l('Criteria deleted successfully.');
            } else {
                $this->errors[] = $this->l('An error occurred while deleting criteria.');
            }
        }

        return $criteria;
    }

    public function processBulkDelete()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $object = new $this->className();

            if (isset($object->noZeroObject) &&
                in_array(0, $this->boxes) && count($this->boxes) > $object->noZeroObject) {
                $this->errors[] = $this->l('You cannot delete all items.');
                return false;
            } else {
                $result = true;
                if ($this->deleted) {
                    foreach ($this->boxes as $id) {
                        $to_delete = new $this->className($id);
                        $to_delete->deleted = 1;
                        $result = $result && $to_delete->update();
                    }
                } else {
                    $result = $object->deleteSelection($this->boxes);
                }

                if ($result) {
                    $this->confirmations[] = $this->l('The selection has been successfully deleted.');
                } else {
                    $this->errors[] = $this->l('An error occurred while deleting this selection.');
                }
            }
        } else {
            $this->errors[] = $this->l('You must select at least one element to delete.');
        }
    }

    public function initContent()
    {
        if (Tools::isSubmit('addDefaultCriteria')) {
            if (StReviewCriteria::addDefaultCriteria()) {
                $this->confirmations[] = $this->l('Default criteria added successfully.');
            } else {
                $this->errors[] = $this->l('Error occurred while adding default criteria.');
            }
        }

        parent::initContent();
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if (empty($this->display)) {
            $this->toolbar_btn['add_default'] = [
                'href' => self::$currentIndex . '&addDefaultCriteria&token=' . $this->token,
                'desc' => $this->l('Add Default Criteria'),
                'class' => 'process-icon-new'
            ];
        }
    }
}
