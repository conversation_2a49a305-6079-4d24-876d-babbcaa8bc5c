{**
 * Store Reviews Widget Template
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 *}

<div class="store-reviews-widget">
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="widget-header text-center">
          <h3>{l s='What Our Customers Say' mod='streviewmaneger'}</h3>
          {if $store_stats && $store_stats.total_reviews > 0}
            <div class="store-rating-summary">
              <div class="overall-rating">
                {for $i=1 to 5}
                  {if $i <= $store_stats.average_rating}
                    <i class="material-icons star filled" style="color: {$star_color};">star</i>
                  {else}
                    <i class="material-icons star" style="color: #ddd;">star_border</i>
                  {/if}
                {/for}
                <span class="rating-text">
                  {$store_stats.average_rating|string_format:"%.1f"} out of 5 
                  ({$store_stats.total_reviews} {if $store_stats.total_reviews == 1}{l s='review' mod='streviewmaneger'}{else}{l s='reviews' mod='streviewmaneger'}{/if})
                </span>
              </div>
            </div>
          {/if}
        </div>
        
        {if $store_reviews}
          <div class="reviews-carousel">
            <div class="row">
              {foreach $store_reviews as $review}
                <div class="col-md-4">
                  <div class="review-card">
                    <div class="review-rating">
                      {for $i=1 to 5}
                        {if $i <= $review.overall_rating}
                          <i class="material-icons star filled" style="color: {$star_color};">star</i>
                        {else}
                          <i class="material-icons star" style="color: #ddd;">star_border</i>
                        {/if}
                      {/for}
                    </div>
                    
                    <div class="review-content">
                      <h5 class="review-title">{$review.title}</h5>
                      <p class="review-text">{$review.content|truncate:150:"...":true}</p>
                    </div>
                    
                    <div class="review-author">
                      <strong>{$review.firstname} {$review.lastname|substr:0:1}.</strong>
                      {if $verified_badge_enabled && $review.verified_purchase}
                        <span class="verified-badge" style="color: {$verified_badge_color};">
                          <i class="material-icons">verified</i>
                          {$verified_badge_text}
                        </span>
                      {/if}
                      <div class="review-date">{$review.date_add|date_format:"%B %Y"}</div>
                    </div>
                  </div>
                </div>
              {/foreach}
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
.store-reviews-widget {
  background: #f8f9fa;
  padding: 50px 0;
  margin-top: 50px;
}

.widget-header h3 {
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
}

.store-rating-summary {
  margin-bottom: 30px;
}

.overall-rating .star {
  font-size: 24px;
  margin: 0 2px;
}

.rating-text {
  margin-left: 10px;
  color: #666;
  font-weight: 500;
}

.review-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.review-card .review-rating {
  margin-bottom: 15px;
}

.review-card .star {
  font-size: 18px;
  margin: 0 1px;
}

.review-content {
  flex: 1;
  margin-bottom: 15px;
}

.review-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.review-text {
  color: #555;
  line-height: 1.5;
  margin-bottom: 0;
}

.review-author {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
  font-size: 0.9rem;
}

.review-author strong {
  color: #333;
}

.verified-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-left: 10px;
}

.verified-badge .material-icons {
  font-size: 14px;
}

.review-date {
  color: #999;
  font-size: 0.8rem;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .store-reviews-widget {
    padding: 30px 0;
  }
  
  .review-card {
    margin-bottom: 15px;
  }
}
</style>
