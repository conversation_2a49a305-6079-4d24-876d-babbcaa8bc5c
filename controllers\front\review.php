<?php
/**
 * Frontend Review Controller
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StreviewmanegerReviewModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
    }

    public function initContent()
    {
        parent::initContent();

        $action = Tools::getValue('action');
        
        switch ($action) {
            case 'submit':
                $this->processReviewSubmission();
                break;
            case 'helpful':
                $this->processHelpfulVote();
                break;
            case 'dispute':
                $this->processDispute();
                break;
            case 'unsubscribe':
                $this->processUnsubscribe();
                break;
            default:
                $this->displayReviewForm();
                break;
        }
    }

    public function displayReviewForm()
    {
        $id_product = (int)Tools::getValue('id_product');
        $type = Tools::getValue('type', 'product');
        $token = Tools::getValue('token');

        // Validate token if provided
        if ($token && !$this->validateToken($token, $id_product)) {
            $this->errors[] = $this->l('Invalid access token.');
            return;
        }

        // Check if customer is logged in
        if (!$this->context->customer->isLogged()) {
            Tools::redirect('index.php?controller=authentication&back=' . urlencode($this->context->link->getModuleLink('streviewmaneger', 'review')));
        }

        $customer = $this->context->customer;

        // Check if customer can review
        if ($type === 'product') {
            if (!$id_product || !StReview::canCustomerReviewProduct($customer->id, $id_product)) {
                $this->errors[] = $this->l('You cannot review this product.');
                return;
            }
            $product = new Product($id_product, false, $this->context->language->id);
            $criteria = StReviewCriteria::getCriteriaByProduct($id_product, $this->context->language->id);
        } else {
            if (!StReview::canCustomerReviewStore($customer->id)) {
                $this->errors[] = $this->l('You cannot review our store.');
                return;
            }
            $product = null;
            $criteria = StReviewCriteria::getActiveCriteriaByType('store', $this->context->language->id);
        }

        $this->context->smarty->assign([
            'type' => $type,
            'product' => $product,
            'criteria' => $criteria,
            'customer' => $customer,
            'action_url' => $this->context->link->getModuleLink('streviewmaneger', 'review', ['action' => 'submit']),
            'errors' => $this->errors
        ]);

        $this->setTemplate('module:streviewmaneger/views/templates/front/review_form.tpl');
    }

    public function processReviewSubmission()
    {
        if (!$this->context->customer->isLogged()) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('You must be logged in.')]));
        }

        $id_product = (int)Tools::getValue('id_product');
        $type = Tools::getValue('type', 'product');
        $title = Tools::getValue('title');
        $content = Tools::getValue('content');
        $ratings = Tools::getValue('ratings', []);

        // Validate input
        if (empty($title) || empty($content)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Title and content are required.')]));
        }

        if (empty($ratings)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Please provide ratings for all criteria.')]));
        }

        // Validate ratings
        foreach ($ratings as $rating) {
            if (!StReviewRating::validateRating($rating)) {
                $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Invalid rating value.')]));
            }
        }

        // Check if customer can review
        $customer = $this->context->customer;
        if ($type === 'product') {
            if (!$id_product || !StReview::canCustomerReviewProduct($customer->id, $id_product)) {
                $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('You cannot review this product.')]));
            }
        } else {
            if (!StReview::canCustomerReviewStore($customer->id)) {
                $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('You cannot review our store.')]));
            }
        }

        // Calculate overall rating
        $overall_rating = StReview::calculateOverallRating($ratings);

        // Create review
        $review = new StReview();
        $review->id_customer = $customer->id;
        $review->id_product = $type === 'product' ? $id_product : null;
        $review->type = $type;
        $review->title = $title;
        $review->content = $content;
        $review->overall_rating = $overall_rating;
        
        // Check if verified purchase
        if ($type === 'product') {
            $review->verified_purchase = $this->isVerifiedPurchase($customer->id, $id_product);
        }

        // Set status based on configuration
        $moderation_enabled = StReviewConfig::get('moderation_enabled', true);
        $auto_approve_verified = StReviewConfig::get('auto_approve_verified', false);
        
        if (!$moderation_enabled || ($auto_approve_verified && $review->verified_purchase)) {
            $review->status = 'approved';
        } else {
            $review->status = 'pending';
        }

        if ($review->add()) {
            // Save individual ratings
            if (!StReviewRating::saveReviewRatings($review->id, $ratings)) {
                $review->delete();
                $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Error saving ratings.')]));
            }

            // Send notification if bad review
            $bad_review_threshold = StReviewConfig::get('bad_review_threshold', 3.0);
            if ($overall_rating < $bad_review_threshold) {
                $this->sendBadReviewAlert($review);
            }

            $message = $review->status === 'approved' 
                ? $this->l('Thank you for your review!')
                : $this->l('Thank you for your review! It will be published after moderation.');

            $this->ajaxDie(json_encode(['success' => true, 'message' => $message]));
        } else {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Error saving review.')]));
        }
    }

    public function processHelpfulVote()
    {
        $id_review = (int)Tools::getValue('id_review');
        $helpful = (bool)Tools::getValue('helpful');

        $review = new StReview($id_review);
        if (!Validate::isLoadedObject($review)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Review not found.')]));
        }

        if ($review->addHelpfulVote($helpful)) {
            $this->ajaxDie(json_encode([
                'success' => true,
                'helpful_yes' => $review->helpful_yes,
                'helpful_no' => $review->helpful_no
            ]));
        } else {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Error recording vote.')]));
        }
    }

    public function processDispute()
    {
        if (!$this->context->customer->isLogged()) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('You must be logged in.')]));
        }

        $id_review = (int)Tools::getValue('id_review');
        $reason = Tools::getValue('reason');
        $description = Tools::getValue('description');

        if (empty($reason) || empty($description)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Reason and description are required.')]));
        }

        $review = new StReview($id_review);
        if (!Validate::isLoadedObject($review)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Review not found.')]));
        }

        // Check if dispute already exists
        if (StReviewDispute::hasDispute($id_review)) {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Dispute already exists for this review.')]));
        }

        $dispute = new StReviewDispute();
        $dispute->id_review = $id_review;
        $dispute->id_customer = $this->context->customer->id;
        $dispute->reason = $reason;
        $dispute->description = $description;
        $dispute->status = 'open';

        if ($dispute->add()) {
            $this->ajaxDie(json_encode(['success' => true, 'message' => $this->l('Dispute submitted successfully.')]));
        } else {
            $this->ajaxDie(json_encode(['success' => false, 'error' => $this->l('Error submitting dispute.')]));
        }
    }

    public function processUnsubscribe()
    {
        $token = Tools::getValue('token');
        
        if (!$token) {
            $this->errors[] = $this->l('Invalid unsubscribe link.');
            return;
        }

        $decrypted = Tools::decrypt($token);
        $parts = explode('|', $decrypted);
        
        if (count($parts) !== 2 || $parts[1] !== 'unsubscribe') {
            $this->errors[] = $this->l('Invalid unsubscribe token.');
            return;
        }

        $id_customer = (int)$parts[0];
        $customer = new Customer($id_customer);
        
        if (!Validate::isLoadedObject($customer)) {
            $this->errors[] = $this->l('Customer not found.');
            return;
        }

        // Add customer to exclusions
        if (StReviewExclusion::addEmailExclusion($customer->email)) {
            // Cancel pending emails
            StReviewEmailQueue::cancelPendingEmails($id_customer);
            
            $this->confirmations[] = $this->l('You have been unsubscribed from review emails.');
        } else {
            $this->errors[] = $this->l('Error processing unsubscribe request.');
        }

        $this->context->smarty->assign([
            'confirmations' => $this->confirmations,
            'errors' => $this->errors
        ]);

        $this->setTemplate('module:streviewmaneger/views/templates/front/unsubscribe.tpl');
    }

    private function validateToken($token, $id_product = null)
    {
        try {
            $decrypted = Tools::decrypt($token);
            $parts = explode('|', $decrypted);
            
            if (count($parts) < 3) {
                return false;
            }

            $id_customer = (int)$parts[0];
            $id_order = (int)$parts[1];
            $product_or_store = $parts[2];

            // Validate customer and order
            $customer = new Customer($id_customer);
            $order = new Order($id_order);

            if (!Validate::isLoadedObject($customer) || !Validate::isLoadedObject($order)) {
                return false;
            }

            // Check if order belongs to customer
            if ($order->id_customer !== $id_customer) {
                return false;
            }

            // Validate product if specified
            if ($id_product && $product_or_store !== 'store') {
                return (int)$product_or_store === $id_product;
            }

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    private function isVerifiedPurchase($id_customer, $id_product)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('order_detail', 'od');
        $sql->innerJoin('orders', 'o', 'od.id_order = o.id_order');
        $sql->where('o.id_customer = ' . (int)$id_customer);
        $sql->where('od.product_id = ' . (int)$id_product);
        $sql->where('o.current_state IN (SELECT id_order_state FROM ' . _DB_PREFIX_ . 'order_state WHERE paid = 1)');

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    private function sendBadReviewAlert($review)
    {
        $alert_enabled = StReviewConfig::get('bad_review_alerts_enabled', true);
        $alert_emails = StReviewConfig::get('bad_review_alert_emails', '');

        if (!$alert_enabled || empty($alert_emails)) {
            return;
        }

        $emails = array_map('trim', explode(',', $alert_emails));
        $customer = new Customer($review->id_customer);

        $template_vars = [
            '{review_title}' => $review->title,
            '{review_content}' => $review->content,
            '{review_rating}' => $review->overall_rating,
            '{customer_name}' => $customer->firstname . ' ' . $customer->lastname,
            '{customer_email}' => $customer->email,
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{review_url}' => $this->context->link->getAdminLink('AdminStReviews') . '&viewst_reviews&id_review=' . $review->id
        ];

        foreach ($emails as $email) {
            if (Validate::isEmail($email)) {
                Mail::Send(
                    (int)$this->context->language->id,
                    'bad_review_alert',
                    Mail::l('Bad Review Alert'),
                    $template_vars,
                    $email,
                    null,
                    null,
                    null,
                    null,
                    null,
                    dirname(__FILE__) . '/../../mails/',
                    false,
                    (int)$this->context->shop->id
                );
            }
        }
    }
}
