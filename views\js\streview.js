/**
 * Advanced Product and Store Review Manager - Frontend JavaScript
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initReviewSystem();
    });

    function initReviewSystem() {
        initHelpfulVotes();
        initSocialSharing();
        initReportSystem();
        initLoadMore();
        initStarAnimations();
    }

    // Helpful votes functionality
    function initHelpfulVotes() {
        const helpfulButtons = document.querySelectorAll('.helpful-btn');
        
        helpfulButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const reviewItem = this.closest('.review-item');
                const reviewId = reviewItem.dataset.reviewId;
                const helpful = this.dataset.helpful === '1';
                
                // Disable buttons to prevent double-clicking
                const allButtons = reviewItem.querySelectorAll('.helpful-btn');
                allButtons.forEach(btn => btn.disabled = true);
                
                fetch(getModuleUrl('review'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        action: 'helpful',
                        id_review: reviewId,
                        helpful: helpful ? '1' : '0'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update vote counts
                        const yesBtn = reviewItem.querySelector('.helpful-btn[data-helpful="1"]');
                        const noBtn = reviewItem.querySelector('.helpful-btn[data-helpful="0"]');
                        
                        yesBtn.innerHTML = `<i class="material-icons">thumb_up</i> Yes (${data.helpful_yes})`;
                        noBtn.innerHTML = `<i class="material-icons">thumb_down</i> No (${data.helpful_no})`;
                        
                        // Show success feedback
                        showNotification('Thank you for your feedback!', 'success');
                        
                        // Keep buttons disabled to prevent multiple votes
                    } else {
                        // Re-enable buttons on error
                        allButtons.forEach(btn => btn.disabled = false);
                        showNotification(data.error || 'Error recording vote', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    allButtons.forEach(btn => btn.disabled = false);
                    showNotification('Error recording vote', 'error');
                });
            });
        });
    }

    // Social sharing functionality
    function initSocialSharing() {
        const shareButtons = document.querySelectorAll('.share-btn');
        
        shareButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const platform = this.dataset.platform;
                const reviewItem = this.closest('.review-item');
                const reviewTitle = reviewItem.querySelector('.review-title').textContent;
                const productName = document.querySelector('h1').textContent || 'this product';
                
                const shareText = `Check out this review of ${productName}: "${reviewTitle}"`;
                const shareUrl = window.location.href;
                
                let shareLink = '';
                
                switch (platform) {
                    case 'facebook':
                        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
                        break;
                    case 'twitter':
                        shareLink = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
                        break;
                }
                
                if (shareLink) {
                    window.open(shareLink, 'share', 'width=600,height=400,scrollbars=yes,resizable=yes');
                }
            });
        });
    }

    // Report system functionality
    function initReportSystem() {
        const reportButtons = document.querySelectorAll('.report-btn');
        const reportModal = document.getElementById('report-modal');
        const reportForm = document.getElementById('report-form');
        const submitReportBtn = document.getElementById('submit-report');
        
        if (!reportModal || !reportForm || !submitReportBtn) {
            return; // Modal elements not found
        }
        
        reportButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const reviewItem = this.closest('.review-item');
                const reviewId = reviewItem.dataset.reviewId;
                
                document.getElementById('report-review-id').value = reviewId;
                
                // Show modal (Bootstrap 4/5 compatible)
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(reportModal);
                    modal.show();
                } else if (typeof $ !== 'undefined') {
                    $(reportModal).modal('show');
                }
            });
        });
        
        submitReportBtn.addEventListener('click', function() {
            const formData = new FormData(reportForm);
            
            // Validate form
            const reason = formData.get('reason');
            const description = formData.get('description');
            
            if (!reason || !description) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }
            
            // Disable submit button
            this.disabled = true;
            this.textContent = 'Submitting...';
            
            fetch(getModuleUrl('review'), {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    action: 'dispute',
                    id_review: formData.get('id_review'),
                    reason: reason,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    
                    // Hide modal
                    if (typeof bootstrap !== 'undefined') {
                        const modal = bootstrap.Modal.getInstance(reportModal);
                        modal.hide();
                    } else if (typeof $ !== 'undefined') {
                        $(reportModal).modal('hide');
                    }
                    
                    // Reset form
                    reportForm.reset();
                } else {
                    showNotification(data.error || 'Error submitting report', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error submitting report', 'error');
            })
            .finally(() => {
                this.disabled = false;
                this.textContent = 'Submit Report';
            });
        });
    }

    // Load more reviews functionality
    function initLoadMore() {
        const loadMoreBtn = document.getElementById('load-more-reviews');
        
        if (!loadMoreBtn) {
            return;
        }
        
        let currentOffset = 10; // Assuming first 10 are already loaded
        
        loadMoreBtn.addEventListener('click', function() {
            const productId = getProductId();
            
            if (!productId) {
                return;
            }
            
            this.disabled = true;
            this.textContent = 'Loading...';
            
            fetch(getModuleUrl('review'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    action: 'load_more',
                    id_product: productId,
                    offset: currentOffset,
                    limit: 10
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.reviews && data.reviews.length > 0) {
                    const reviewsList = document.querySelector('.reviews-list');
                    const loadMoreSection = document.querySelector('.load-more-section');
                    
                    // Insert new reviews before load more button
                    data.reviews.forEach(review => {
                        const reviewElement = createReviewElement(review);
                        reviewsList.insertBefore(reviewElement, loadMoreSection);
                    });
                    
                    currentOffset += data.reviews.length;
                    
                    // Hide load more button if no more reviews
                    if (data.reviews.length < 10) {
                        loadMoreSection.style.display = 'none';
                    }
                    
                    // Re-initialize functionality for new elements
                    initHelpfulVotes();
                    initSocialSharing();
                    initReportSystem();
                } else {
                    this.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error loading more reviews', 'error');
            })
            .finally(() => {
                this.disabled = false;
                this.textContent = 'Load More Reviews';
            });
        });
    }

    // Star animations
    function initStarAnimations() {
        const stars = document.querySelectorAll('.star');
        
        stars.forEach(function(star) {
            star.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
            });
            
            star.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }

    // Helper functions
    function getModuleUrl(controller) {
        // Try to get from global variable or construct from current URL
        if (typeof prestashop !== 'undefined' && prestashop.urls && prestashop.urls.base_url) {
            return prestashop.urls.base_url + 'module/streviewmaneger/' + controller;
        }
        
        // Fallback: construct from current location
        const baseUrl = window.location.origin + window.location.pathname.split('/').slice(0, -1).join('/');
        return baseUrl + '/module/streviewmaneger/' + controller;
    }

    function getProductId() {
        // Try multiple methods to get product ID
        const urlParams = new URLSearchParams(window.location.search);
        let productId = urlParams.get('id_product');
        
        if (!productId) {
            // Try to get from page data
            const productData = document.querySelector('[data-product-id]');
            if (productData) {
                productId = productData.dataset.productId;
            }
        }
        
        if (!productId) {
            // Try to get from global prestashop object
            if (typeof prestashop !== 'undefined' && prestashop.page && prestashop.page.page_name === 'product') {
                productId = prestashop.page.id_product;
            }
        }
        
        return productId;
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
        
        // Handle close button
        const closeBtn = notification.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });
        }
    }

    function createReviewElement(review) {
        // This would create a new review element from the review data
        // Implementation depends on the exact structure needed
        const div = document.createElement('div');
        div.className = 'review-item';
        div.dataset.reviewId = review.id_review;
        
        // Add review HTML content here
        // This is a simplified version - you'd want to match the template structure
        div.innerHTML = `
            <div class="review-header">
                <div class="reviewer-info">
                    <strong class="reviewer-name">${review.firstname} ${review.lastname.charAt(0)}.</strong>
                </div>
                <div class="review-date">${review.date_add}</div>
            </div>
            <div class="review-content">
                <h5 class="review-title">${review.title}</h5>
                <p class="review-text">${review.content}</p>
            </div>
        `;
        
        return div;
    }

    // Export for global access if needed
    window.StreviewManager = {
        init: initReviewSystem,
        showNotification: showNotification
    };

})();
