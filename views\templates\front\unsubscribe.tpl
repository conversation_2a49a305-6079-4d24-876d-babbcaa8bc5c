{**
 * Unsubscribe Template
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 *}

{extends file='page.tpl'}

{block name='page_title'}
  {l s='Email Preferences' mod='streviewmaneger'}
{/block}

{block name='page_content'}
  <div class="unsubscribe-container">
    
    {if $confirmations}
      <div class="alert alert-success">
        <ul class="mb-0">
          {foreach $confirmations as $confirmation}
            <li>{$confirmation}</li>
          {/foreach}
        </ul>
      </div>
    {/if}

    {if $errors}
      <div class="alert alert-danger">
        <ul class="mb-0">
          {foreach $errors as $error}
            <li>{$error}</li>
          {/foreach}
        </ul>
      </div>
    {/if}

    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="material-icons">email</i>
          {l s='Review Email Preferences' mod='streviewmaneger'}
        </h3>
      </div>
      
      <div class="card-body text-center">
        {if $confirmations}
          <div class="success-content">
            <i class="material-icons success-icon">check_circle</i>
            <h4>{l s='Successfully Unsubscribed' mod='streviewmaneger'}</h4>
            <p>{l s='You have been removed from our review email list. You will no longer receive review request emails from us.' mod='streviewmaneger'}</p>
            <p><strong>{l s='Note:' mod='streviewmaneger'}</strong> {l s='You may still receive other important emails related to your orders and account.' mod='streviewmaneger'}</p>
            
            <div class="action-buttons">
              <a href="{$urls.base_url}" class="btn btn-primary">
                <i class="material-icons">home</i>
                {l s='Return to Store' mod='streviewmaneger'}
              </a>
            </div>
          </div>
        {else}
          <div class="info-content">
            <i class="material-icons info-icon">info</i>
            <h4>{l s='Manage Your Email Preferences' mod='streviewmaneger'}</h4>
            <p>{l s='We respect your privacy and want to make sure you only receive emails you want.' mod='streviewmaneger'}</p>
            
            <div class="email-types">
              <div class="email-type">
                <h5>{l s='Review Request Emails' mod='streviewmaneger'}</h5>
                <p>{l s='These emails ask you to review products you have purchased and share your experience with other customers.' mod='streviewmaneger'}</p>
              </div>
            </div>
            
            <div class="action-buttons">
              <a href="{$urls.base_url}" class="btn btn-secondary">
                <i class="material-icons">arrow_back</i>
                {l s='Keep Receiving Emails' mod='streviewmaneger'}
              </a>
            </div>
          </div>
        {/if}
      </div>
    </div>

    <div class="additional-info">
      <div class="row">
        <div class="col-md-6">
          <div class="info-box">
            <h5>{l s='Why We Send Review Emails' mod='streviewmaneger'}</h5>
            <ul>
              <li>{l s='Help other customers make informed decisions' mod='streviewmaneger'}</li>
              <li>{l s='Improve our products and services' mod='streviewmaneger'}</li>
              <li>{l s='Build trust in our community' mod='streviewmaneger'}</li>
              <li>{l s='Recognize great products and service' mod='streviewmaneger'}</li>
            </ul>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="info-box">
            <h5>{l s='Your Privacy Matters' mod='streviewmaneger'}</h5>
            <ul>
              <li>{l s='We never share your email with third parties' mod='streviewmaneger'}</li>
              <li>{l s='You can unsubscribe at any time' mod='streviewmaneger'}</li>
              <li>{l s='We only send relevant, helpful emails' mod='streviewmaneger'}</li>
              <li>{l s='Your data is secure and protected' mod='streviewmaneger'}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
{/block}

{block name='page_footer'}
  <style>
    .unsubscribe-container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .success-icon,
    .info-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }
    
    .success-icon {
      color: #28a745;
    }
    
    .info-icon {
      color: #17a2b8;
    }
    
    .success-content h4,
    .info-content h4 {
      margin-bottom: 20px;
      color: #333;
    }
    
    .success-content p,
    .info-content p {
      margin-bottom: 15px;
      color: #666;
      line-height: 1.6;
    }
    
    .action-buttons {
      margin: 30px 0;
    }
    
    .action-buttons .btn {
      margin: 0 10px;
      padding: 12px 30px;
      border-radius: 25px;
      font-weight: 600;
    }
    
    .email-types {
      margin: 30px 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      text-align: left;
    }
    
    .email-type h5 {
      color: #333;
      margin-bottom: 10px;
    }
    
    .email-type p {
      color: #666;
      margin-bottom: 0;
    }
    
    .additional-info {
      margin-top: 40px;
    }
    
    .info-box {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      height: 100%;
    }
    
    .info-box h5 {
      color: #333;
      margin-bottom: 15px;
      font-weight: 600;
    }
    
    .info-box ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .info-box li {
      padding: 8px 0;
      color: #666;
      position: relative;
      padding-left: 25px;
    }
    
    .info-box li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #28a745;
      font-weight: bold;
    }
    
    @media (max-width: 768px) {
      .action-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
      }
      
      .additional-info .col-md-6 {
        margin-bottom: 20px;
      }
    }
  </style>
{/block}
