<?php
/**
 * Review Criteria Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewCriteria extends ObjectModel
{
    /** @var string Name */
    public $name;

    /** @var bool Active status */
    public $active;

    /** @var int Position */
    public $position;

    /** @var string Type (product, store, both) */
    public $type;

    /** @var string Date add */
    public $date_add;

    /** @var string Date update */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_criteria',
        'primary' => 'id_criteria',
        'multilang' => true,
        'fields' => [
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool', 'required' => true],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['product', 'store', 'both']],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],

            // Lang fields
            'name' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get all active criteria for a specific type
     *
     * @param string $type
     * @param int $id_lang
     * @return array
     */
    public static function getActiveCriteriaByType($type = 'product', $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('c.*, cl.name');
        $sql->from('st_review_criteria', 'c');
        $sql->leftJoin('st_review_criteria_lang', 'cl', 'c.id_criteria = cl.id_criteria AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('c.active = 1');
        $sql->where('c.type = "' . pSQL($type) . '" OR c.type = "both"');
        $sql->orderBy('c.position ASC, c.id_criteria ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get criteria for product category
     *
     * @param int $id_category
     * @param int $id_lang
     * @return array
     */
    public static function getCriteriaByCategory($id_category, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        // For now, return all product criteria
        // This can be extended to support category-specific criteria
        return self::getActiveCriteriaByType('product', $id_lang);
    }

    /**
     * Get criteria for specific product
     *
     * @param int $id_product
     * @param int $id_lang
     * @return array
     */
    public static function getCriteriaByProduct($id_product, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        // For now, return all product criteria
        // This can be extended to support product-specific criteria
        return self::getActiveCriteriaByType('product', $id_lang);
    }

    /**
     * Update positions
     *
     * @param array $positions
     * @return bool
     */
    public static function updatePositions($positions)
    {
        foreach ($positions as $position => $id_criteria) {
            $sql = 'UPDATE `' . _DB_PREFIX_ . 'st_review_criteria` 
                    SET `position` = ' . (int)$position . ' 
                    WHERE `id_criteria` = ' . (int)$id_criteria;
            
            if (!Db::getInstance()->execute($sql)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get next position
     *
     * @return int
     */
    public static function getNextPosition()
    {
        $sql = 'SELECT MAX(position) + 1 as next_position FROM `' . _DB_PREFIX_ . 'st_review_criteria`';
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
        return $result['next_position'] ? (int)$result['next_position'] : 1;
    }

    /**
     * Delete criteria and related data
     *
     * @return bool
     */
    public function delete()
    {
        // Delete related ratings first
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_ratings` WHERE `id_criteria` = ' . (int)$this->id;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        return parent::delete();
    }

    /**
     * Add default criteria
     *
     * @return bool
     */
    public static function addDefaultCriteria()
    {
        $default_criteria = [
            [
                'type' => 'product',
                'names' => [
                    'en' => 'Quality',
                    'es' => 'Calidad',
                    'fr' => 'Qualité',
                    'de' => 'Qualität',
                    'it' => 'Qualità'
                ]
            ],
            [
                'type' => 'product',
                'names' => [
                    'en' => 'Value for Money',
                    'es' => 'Relación Calidad-Precio',
                    'fr' => 'Rapport Qualité-Prix',
                    'de' => 'Preis-Leistungs-Verhältnis',
                    'it' => 'Rapporto Qualità-Prezzo'
                ]
            ],
            [
                'type' => 'product',
                'names' => [
                    'en' => 'Design',
                    'es' => 'Diseño',
                    'fr' => 'Design',
                    'de' => 'Design',
                    'it' => 'Design'
                ]
            ],
            [
                'type' => 'store',
                'names' => [
                    'en' => 'Customer Service',
                    'es' => 'Atención al Cliente',
                    'fr' => 'Service Client',
                    'de' => 'Kundenservice',
                    'it' => 'Servizio Clienti'
                ]
            ],
            [
                'type' => 'store',
                'names' => [
                    'en' => 'Shipping Speed',
                    'es' => 'Velocidad de Envío',
                    'fr' => 'Rapidité de Livraison',
                    'de' => 'Versandgeschwindigkeit',
                    'it' => 'Velocità di Spedizione'
                ]
            ],
            [
                'type' => 'store',
                'names' => [
                    'en' => 'Website Experience',
                    'es' => 'Experiencia Web',
                    'fr' => 'Expérience Web',
                    'de' => 'Website-Erfahrung',
                    'it' => 'Esperienza Web'
                ]
            ]
        ];

        $languages = Language::getLanguages(true);
        $position = 1;

        foreach ($default_criteria as $criteria_data) {
            $criteria = new StReviewCriteria();
            $criteria->type = $criteria_data['type'];
            $criteria->active = 1;
            $criteria->position = $position++;

            // Set names for all languages
            $criteria->name = [];
            foreach ($languages as $language) {
                $lang_code = strtolower($language['iso_code']);
                if (isset($criteria_data['names'][$lang_code])) {
                    $criteria->name[$language['id_lang']] = $criteria_data['names'][$lang_code];
                } else {
                    // Fallback to English
                    $criteria->name[$language['id_lang']] = $criteria_data['names']['en'];
                }
            }

            if (!$criteria->add()) {
                return false;
            }
        }

        return true;
    }
}
