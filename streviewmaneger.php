<?php
/**
 * Advanced Product and Store Review Manager
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/classes/StReviewCriteria.php';
require_once dirname(__FILE__) . '/classes/StReview.php';
require_once dirname(__FILE__) . '/classes/StReviewRating.php';
require_once dirname(__FILE__) . '/classes/StReviewReply.php';
require_once dirname(__FILE__) . '/classes/StReviewDispute.php';
require_once dirname(__FILE__) . '/classes/StReviewEmailQueue.php';
require_once dirname(__FILE__) . '/classes/StReviewExclusion.php';
require_once dirname(__FILE__) . '/classes/StReviewConfig.php';

class StReviewManeger extends Module
{
    protected $config_form = false;

    public function __construct()
    {
        $this->name = 'streviewmaneger';
        $this->tab = 'administration';
        $this->version = '1.0.0';
        $this->author = 'Augment Code';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '9.0.0',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Advanced Product and Store Review Manager');
        $this->description = $this->l('Comprehensive review management system with multi-criteria ratings, email automation, moderation tools, and advanced features.');

        $this->confirmUninstall = $this->l('Are you sure you want to uninstall this module? All review data will be lost.');
    }

    /**
     * Don't forget to create update methods if needed:
     * http://doc.prestashop.com/display/PS16/Enabling+the+Auto-Update
     */
    public function install()
    {
        Configuration::updateValue('STREVIEW_LIVE_MODE', false);

        return parent::install() &&
            $this->registerHook('header') &&
            $this->registerHook('backOfficeHeader') &&
            $this->registerHook('displayProductExtraContent') &&
            $this->registerHook('displayProductListReviews') &&
            $this->registerHook('displayFooterProduct') &&
            $this->registerHook('displayOrderConfirmation') &&
            $this->registerHook('actionValidateOrder') &&
            $this->registerHook('actionOrderStatusUpdate') &&
            $this->registerHook('displayCustomerAccount') &&
            $this->registerHook('displayAdminProductsExtra') &&
            $this->registerHook('displayAdminOrderTabContent') &&
            $this->registerHook('displayAdminOrderTabLink') &&
            $this->createTables() &&
            $this->installTabs() &&
            $this->installDefaultConfiguration();
    }

    public function uninstall()
    {
        Configuration::deleteByName('STREVIEW_LIVE_MODE');

        return parent::uninstall() &&
            $this->uninstallTabs() &&
            $this->dropTables();
    }

    /**
     * Create database tables
     */
    protected function createTables()
    {
        $sql = [];

        // Review criteria table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_criteria` (
            `id_criteria` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `active` tinyint(1) NOT NULL DEFAULT 1,
            `position` int(11) NOT NULL DEFAULT 0,
            `type` enum("product", "store", "both") NOT NULL DEFAULT "product",
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_criteria`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Review criteria lang table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_criteria_lang` (
            `id_criteria` int(11) NOT NULL,
            `id_lang` int(11) NOT NULL,
            `name` varchar(255) NOT NULL,
            PRIMARY KEY (`id_criteria`, `id_lang`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Reviews table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_reviews` (
            `id_review` int(11) NOT NULL AUTO_INCREMENT,
            `id_customer` int(11) NOT NULL,
            `id_product` int(11) NULL,
            `id_order` int(11) NULL,
            `type` enum("product", "store") NOT NULL DEFAULT "product",
            `title` varchar(255) NOT NULL,
            `content` text NOT NULL,
            `overall_rating` decimal(3,2) NOT NULL,
            `status` enum("pending", "approved", "rejected") NOT NULL DEFAULT "pending",
            `verified_purchase` tinyint(1) NOT NULL DEFAULT 0,
            `helpful_yes` int(11) NOT NULL DEFAULT 0,
            `helpful_no` int(11) NOT NULL DEFAULT 0,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_review`),
            KEY `idx_customer` (`id_customer`),
            KEY `idx_product` (`id_product`),
            KEY `idx_order` (`id_order`),
            KEY `idx_status` (`status`),
            KEY `idx_type` (`type`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Review ratings table (for individual criteria)
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_ratings` (
            `id_rating` int(11) NOT NULL AUTO_INCREMENT,
            `id_review` int(11) NOT NULL,
            `id_criteria` int(11) NOT NULL,
            `rating` decimal(3,2) NOT NULL,
            PRIMARY KEY (`id_rating`),
            UNIQUE KEY `review_criteria` (`id_review`, `id_criteria`),
            KEY `idx_review` (`id_review`),
            KEY `idx_criteria` (`id_criteria`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Review replies table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_replies` (
            `id_reply` int(11) NOT NULL AUTO_INCREMENT,
            `id_review` int(11) NOT NULL,
            `id_employee` int(11) NOT NULL,
            `content` text NOT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_reply`),
            KEY `idx_review` (`id_review`),
            KEY `idx_employee` (`id_employee`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Review disputes table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_disputes` (
            `id_dispute` int(11) NOT NULL AUTO_INCREMENT,
            `id_review` int(11) NOT NULL,
            `id_customer` int(11) NOT NULL,
            `reason` varchar(255) NOT NULL,
            `description` text NOT NULL,
            `status` enum("open", "in_progress", "resolved", "rejected") NOT NULL DEFAULT "open",
            `resolution` text NULL,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_dispute`),
            KEY `idx_review` (`id_review`),
            KEY `idx_customer` (`id_customer`),
            KEY `idx_status` (`status`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Email queue table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_email_queue` (
            `id_queue` int(11) NOT NULL AUTO_INCREMENT,
            `id_customer` int(11) NOT NULL,
            `id_order` int(11) NOT NULL,
            `email_type` enum("first_request", "reminder") NOT NULL DEFAULT "first_request",
            `review_type` enum("product", "store") NOT NULL DEFAULT "product",
            `status` enum("pending", "sent", "failed") NOT NULL DEFAULT "pending",
            `scheduled_date` datetime NOT NULL,
            `sent_date` datetime NULL,
            `attempts` int(11) NOT NULL DEFAULT 0,
            `last_error` text NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_queue`),
            KEY `idx_customer` (`id_customer`),
            KEY `idx_order` (`id_order`),
            KEY `idx_status` (`status`),
            KEY `idx_scheduled` (`scheduled_date`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Exclusions table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_exclusions` (
            `id_exclusion` int(11) NOT NULL AUTO_INCREMENT,
            `type` enum("email", "domain", "customer_group", "product") NOT NULL,
            `value` varchar(255) NOT NULL,
            `active` tinyint(1) NOT NULL DEFAULT 1,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_exclusion`),
            KEY `idx_type` (`type`),
            KEY `idx_active` (`active`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Configuration table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_review_config` (
            `id_config` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `value` text NULL,
            `id_shop` int(11) NULL,
            `id_shop_group` int(11) NULL,
            PRIMARY KEY (`id_config`),
            UNIQUE KEY `config_shop` (`name`, `id_shop`, `id_shop_group`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Drop database tables
     */
    protected function dropTables()
    {
        $sql = [
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_config`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_exclusions`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_email_queue`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_disputes`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_replies`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_ratings`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_reviews`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_criteria_lang`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_review_criteria`'
        ];

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Install admin tabs
     */
    protected function installTabs()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminStReviewManager';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Review Manager';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('AdminCatalog');
        $tab->module = $this->name;
        return $tab->add();
    }

    /**
     * Uninstall admin tabs
     */
    protected function uninstallTabs()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminStReviewManager');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    /**
     * Install default configuration
     */
    protected function installDefaultConfiguration()
    {
        $default_configs = [
            'STREVIEW_PRODUCT_REVIEWS_ENABLED' => 1,
            'STREVIEW_STORE_REVIEWS_ENABLED' => 1,
            'STREVIEW_MODERATION_ENABLED' => 1,
            'STREVIEW_EMAIL_ENABLED' => 1,
            'STREVIEW_EMAIL_DELAY_DAYS' => 7,
            'STREVIEW_REMINDER_ENABLED' => 1,
            'STREVIEW_REMINDER_DELAY_DAYS' => 14,
            'STREVIEW_STAR_COLOR' => '#FFD700',
            'STREVIEW_STAR_SIZE' => 20,
            'STREVIEW_VERIFIED_BADGE_ENABLED' => 1,
            'STREVIEW_SOCIAL_SHARING_ENABLED' => 1,
            'STREVIEW_GOOGLE_MY_BUSINESS_ENABLED' => 0,
            'STREVIEW_BAD_REVIEW_THRESHOLD' => 3.0,
            'STREVIEW_BAD_REVIEW_ALERTS_ENABLED' => 1
        ];

        foreach ($default_configs as $name => $value) {
            Configuration::updateValue($name, $value);
        }

        return true;
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        /**
         * If values have been submitted in the form, process.
         */
        if (((bool)Tools::isSubmit('submitStReviewManegerModule')) == true) {
            $this->postProcess();
        }

        $this->context->smarty->assign('module_dir', $this->_path);

        $output = $this->context->smarty->fetch($this->local_path.'views/templates/admin/configure.tpl');

        return $output.$this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitStReviewManegerModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            .'&configure='.$this->name.'&tab_module='.$this->tab.'&module_name='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = [
            'fields_value' => $this->getConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm([$this->getConfigForm()]);
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm()
    {
        return [
            'form' => [
                'legend' => [
                'title' => $this->l('Settings'),
                'icon' => 'icon-cogs',
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Live mode'),
                        'name' => 'STREVIEW_LIVE_MODE',
                        'is_bool' => true,
                        'desc' => $this->l('Use this module in live mode'),
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                ],
                'submit' => [
                    'title' => $this->l('Save'),
                ],
            ],
        ];
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues()
    {
        return [
            'STREVIEW_LIVE_MODE' => Configuration::get('STREVIEW_LIVE_MODE', true),
        ];
    }

    /**
     * Save form data.
     */
    protected function postProcess()
    {
        $form_values = $this->getConfigFormValues();

        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be loaded in the BO.
     */
    public function hookBackOfficeHeader()
    {
        if (Tools::getValue('module_name') == $this->name) {
            $this->context->controller->addJS($this->_path.'views/js/back.js');
            $this->context->controller->addCSS($this->_path.'views/css/back.css');
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be added on the FO.
     */
    public function hookHeader()
    {
        $this->context->controller->addJS($this->_path.'/views/js/front.js');
        $this->context->controller->addCSS($this->_path.'/views/css/front.css');
    }
}
