<?php
/**
 * Hook for displaying product reviews in product page tabs
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class HookDisplayProductTabContent
{
    private $module;
    private $context;

    public function __construct($module)
    {
        $this->module = $module;
        $this->context = Context::getContext();
    }

    public function execute($params)
    {
        // Check if product reviews are enabled
        if (!StReviewConfig::get('product_reviews_enabled', true)) {
            return '';
        }

        $product = $params['product'];
        $id_product = (int)$product['id_product'];

        // Get reviews for this product
        $reviews = StReview::getProductReviews($id_product, 'approved', 10, 0);
        
        // Get rating statistics
        $rating_stats = StReview::getProductRatingStats($id_product);
        
        // Get criteria averages
        $criteria_averages = StReviewRating::getProductCriteriaAverages($id_product, $this->context->language->id);

        // Check if customer can review this product
        $can_review = false;
        if ($this->context->customer->isLogged()) {
            $can_review = StReview::canCustomerReviewProduct($this->context->customer->id, $id_product);
        }

        // Get review form URL
        $review_form_url = $this->context->link->getModuleLink('streviewmaneger', 'review', [
            'id_product' => $id_product,
            'type' => 'product'
        ]);

        $this->context->smarty->assign([
            'product' => $product,
            'reviews' => $reviews,
            'rating_stats' => $rating_stats,
            'criteria_averages' => $criteria_averages,
            'can_review' => $can_review,
            'review_form_url' => $review_form_url,
            'customer_logged' => $this->context->customer->isLogged(),
            'login_url' => $this->context->link->getPageLink('authentication'),
            'star_color' => StReviewConfig::get('star_color', '#FFD700'),
            'star_size' => StReviewConfig::get('star_size', 20),
            'verified_badge_enabled' => StReviewConfig::get('verified_badge_enabled', true),
            'verified_badge_text' => StReviewConfig::get('verified_badge_text', 'Verified Purchase'),
            'verified_badge_color' => StReviewConfig::get('verified_badge_color', '#28a745'),
            'social_sharing_enabled' => StReviewConfig::get('social_sharing_enabled', true),
            'facebook_sharing' => StReviewConfig::get('facebook_sharing', true),
            'twitter_sharing' => StReviewConfig::get('twitter_sharing', true)
        ]);

        return $this->module->display($this->module->getLocalPath(), 'views/templates/hook/product_reviews.tpl');
    }
}

class HookDisplayProductTab
{
    private $module;
    private $context;

    public function __construct($module)
    {
        $this->module = $module;
        $this->context = Context::getContext();
    }

    public function execute($params)
    {
        // Check if product reviews are enabled
        if (!StReviewConfig::get('product_reviews_enabled', true)) {
            return '';
        }

        $product = $params['product'];
        $id_product = (int)$product['id_product'];

        // Get review count for tab title
        $review_count = 0;
        $rating_stats = StReview::getProductRatingStats($id_product);
        if ($rating_stats) {
            $review_count = (int)$rating_stats['total_reviews'];
        }

        $this->context->smarty->assign([
            'review_count' => $review_count,
            'average_rating' => $rating_stats ? round($rating_stats['average_rating'], 1) : 0
        ]);

        return $this->module->display($this->module->getLocalPath(), 'views/templates/hook/product_tab.tpl');
    }
}

class HookActionOrderStatusPostUpdate
{
    private $module;

    public function __construct($module)
    {
        $this->module = $module;
    }

    public function execute($params)
    {
        // Check if email requests are enabled
        if (!StReviewConfig::get('email_enabled', true)) {
            return;
        }

        $new_order_status = $params['newOrderStatus'];
        $id_order = (int)$params['id_order'];

        // Get configured order statuses that trigger review requests
        $trigger_statuses = StReviewConfig::get('email_order_statuses', [2, 3, 4]); // Paid, Preparation, Shipped
        
        if (!is_array($trigger_statuses)) {
            $trigger_statuses = [2, 3, 4];
        }

        // Check if this status change should trigger a review request
        if (!in_array($new_order_status->id, $trigger_statuses)) {
            return;
        }

        $order = new Order($id_order);
        if (!Validate::isLoadedObject($order)) {
            return;
        }

        $delay_days = (int)StReviewConfig::get('email_delay_days', 7);

        // Add product review requests if enabled
        $product_reviews_enabled = StReviewConfig::get('product_reviews_enabled', true);
        if ($product_reviews_enabled) {
            StReviewEmailQueue::addToQueue($order->id_customer, $id_order, 'first_request', 'product', $delay_days);
        }

        // Add store review request if enabled
        $store_reviews_enabled = StReviewConfig::get('store_reviews_enabled', true);
        if ($store_reviews_enabled) {
            StReviewEmailQueue::addToQueue($order->id_customer, $id_order, 'first_request', 'store', $delay_days);
        }
    }
}

class HookDisplayHeader
{
    private $module;
    private $context;

    public function __construct($module)
    {
        $this->module = $module;
        $this->context = Context::getContext();
    }

    public function execute($params)
    {
        // Add CSS and JS for reviews
        $this->context->controller->addCSS($this->module->getPathUri() . 'views/css/streview.css');
        $this->context->controller->addJS($this->module->getPathUri() . 'views/js/streview.js');

        // Add structured data for reviews if on product page
        if ($this->context->controller instanceof ProductController) {
            $id_product = (int)Tools::getValue('id_product');
            if ($id_product) {
                $rating_stats = StReview::getProductRatingStats($id_product);
                if ($rating_stats && $rating_stats['total_reviews'] > 0) {
                    $structured_data = $this->generateStructuredData($id_product, $rating_stats);
                    return '<script type="application/ld+json">' . json_encode($structured_data) . '</script>';
                }
            }
        }

        return '';
    }

    private function generateStructuredData($id_product, $rating_stats)
    {
        $product = new Product($id_product, false, $this->context->language->id);
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $product->name,
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => round($rating_stats['average_rating'], 1),
                'reviewCount' => (int)$rating_stats['total_reviews'],
                'bestRating' => 5,
                'worstRating' => 1
            ]
        ];
    }
}

class HookDisplayFooter
{
    private $module;
    private $context;

    public function __construct($module)
    {
        $this->module = $module;
        $this->context = Context::getContext();
    }

    public function execute($params)
    {
        // Display store reviews widget if enabled
        if (!StReviewConfig::get('store_reviews_enabled', true)) {
            return '';
        }

        // Get recent store reviews
        $store_reviews = StReview::getStoreReviews('approved', 3, 0);
        $store_stats = StReview::getStoreRatingStats();

        if (empty($store_reviews)) {
            return '';
        }

        $this->context->smarty->assign([
            'store_reviews' => $store_reviews,
            'store_stats' => $store_stats,
            'star_color' => StReviewConfig::get('star_color', '#FFD700'),
            'verified_badge_enabled' => StReviewConfig::get('verified_badge_enabled', true),
            'verified_badge_text' => StReviewConfig::get('verified_badge_text', 'Verified Purchase'),
            'verified_badge_color' => StReviewConfig::get('verified_badge_color', '#28a745')
        ]);

        return $this->module->display($this->module->getLocalPath(), 'views/templates/hook/store_reviews_widget.tpl');
    }
}
