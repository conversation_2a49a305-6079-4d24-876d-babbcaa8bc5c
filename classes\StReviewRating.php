<?php
/**
 * Review Rating Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewRating extends ObjectModel
{
    /** @var int Review ID */
    public $id_review;

    /** @var int Criteria ID */
    public $id_criteria;

    /** @var float Rating */
    public $rating;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_ratings',
        'primary' => 'id_rating',
        'fields' => [
            'id_review' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_criteria' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'rating' => ['type' => self::TYPE_FLOAT, 'validate' => 'isFloat', 'required' => true],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get ratings by review ID
     *
     * @param int $id_review
     * @param int $id_lang
     * @return array
     */
    public static function getRatingsByReview($id_review, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('rr.*, c.name as criteria_name');
        $sql->from('st_review_ratings', 'rr');
        $sql->leftJoin('st_review_criteria', 'c', 'rr.id_criteria = c.id_criteria');
        $sql->leftJoin('st_review_criteria_lang', 'cl', 'c.id_criteria = cl.id_criteria AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('rr.id_review = ' . (int)$id_review);
        $sql->orderBy('c.position ASC, c.id_criteria ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get average ratings by criteria for a product
     *
     * @param int $id_product
     * @param int $id_lang
     * @return array
     */
    public static function getProductCriteriaAverages($id_product, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('
            rr.id_criteria,
            cl.name as criteria_name,
            AVG(rr.rating) as average_rating,
            COUNT(rr.rating) as total_ratings
        ');
        $sql->from('st_review_ratings', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->innerJoin('st_review_criteria', 'c', 'rr.id_criteria = c.id_criteria');
        $sql->leftJoin('st_review_criteria_lang', 'cl', 'c.id_criteria = cl.id_criteria AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('r.id_product = ' . (int)$id_product);
        $sql->where('r.type = "product"');
        $sql->where('r.status = "approved"');
        $sql->groupBy('rr.id_criteria');
        $sql->orderBy('c.position ASC, c.id_criteria ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get average ratings by criteria for store
     *
     * @param int $id_lang
     * @return array
     */
    public static function getStoreCriteriaAverages($id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('
            rr.id_criteria,
            cl.name as criteria_name,
            AVG(rr.rating) as average_rating,
            COUNT(rr.rating) as total_ratings
        ');
        $sql->from('st_review_ratings', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->innerJoin('st_review_criteria', 'c', 'rr.id_criteria = c.id_criteria');
        $sql->leftJoin('st_review_criteria_lang', 'cl', 'c.id_criteria = cl.id_criteria AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('r.type = "store"');
        $sql->where('r.status = "approved"');
        $sql->groupBy('rr.id_criteria');
        $sql->orderBy('c.position ASC, c.id_criteria ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Save multiple ratings for a review
     *
     * @param int $id_review
     * @param array $ratings
     * @return bool
     */
    public static function saveReviewRatings($id_review, $ratings)
    {
        // First, delete existing ratings for this review
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_ratings` WHERE `id_review` = ' . (int)$id_review;
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Insert new ratings
        foreach ($ratings as $id_criteria => $rating) {
            $review_rating = new StReviewRating();
            $review_rating->id_review = (int)$id_review;
            $review_rating->id_criteria = (int)$id_criteria;
            $review_rating->rating = (float)$rating;

            if (!$review_rating->add()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get rating distribution for a criteria
     *
     * @param int $id_criteria
     * @param int $id_product (optional, for product-specific distribution)
     * @return array
     */
    public static function getCriteriaRatingDistribution($id_criteria, $id_product = null)
    {
        $sql = new DbQuery();
        $sql->select('
            FLOOR(rr.rating) as rating_floor,
            COUNT(*) as count
        ');
        $sql->from('st_review_ratings', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->where('rr.id_criteria = ' . (int)$id_criteria);
        $sql->where('r.status = "approved"');

        if ($id_product) {
            $sql->where('r.id_product = ' . (int)$id_product);
            $sql->where('r.type = "product"');
        } else {
            $sql->where('r.type = "store"');
        }

        $sql->groupBy('FLOOR(rr.rating)');
        $sql->orderBy('rating_floor DESC');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        // Initialize distribution array
        $distribution = [
            5 => 0,
            4 => 0,
            3 => 0,
            2 => 0,
            1 => 0
        ];

        // Fill with actual data
        foreach ($results as $result) {
            $rating = (int)$result['rating_floor'];
            if ($rating >= 1 && $rating <= 5) {
                $distribution[$rating] = (int)$result['count'];
            }
        }

        return $distribution;
    }

    /**
     * Get top rated products by criteria
     *
     * @param int $id_criteria
     * @param int $limit
     * @param int $id_lang
     * @return array
     */
    public static function getTopRatedProductsByCriteria($id_criteria, $limit = 10, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('
            r.id_product,
            pl.name as product_name,
            AVG(rr.rating) as average_rating,
            COUNT(rr.rating) as total_ratings
        ');
        $sql->from('st_review_ratings', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->innerJoin('product_lang', 'pl', 'r.id_product = pl.id_product AND pl.id_lang = ' . (int)$id_lang);
        $sql->where('rr.id_criteria = ' . (int)$id_criteria);
        $sql->where('r.type = "product"');
        $sql->where('r.status = "approved"');
        $sql->groupBy('r.id_product');
        $sql->having('COUNT(rr.rating) >= 3'); // Minimum 3 ratings
        $sql->orderBy('average_rating DESC, total_ratings DESC');
        $sql->limit((int)$limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Validate rating value
     *
     * @param float $rating
     * @return bool
     */
    public static function validateRating($rating)
    {
        return is_numeric($rating) && $rating >= 1 && $rating <= 5;
    }

    /**
     * Get criteria statistics
     *
     * @param int $id_criteria
     * @param string $type
     * @param int $id_product
     * @return array
     */
    public static function getCriteriaStats($id_criteria, $type = 'product', $id_product = null)
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_ratings,
            AVG(rr.rating) as average_rating,
            MIN(rr.rating) as min_rating,
            MAX(rr.rating) as max_rating,
            STDDEV(rr.rating) as std_deviation
        ');
        $sql->from('st_review_ratings', 'rr');
        $sql->innerJoin('st_reviews', 'r', 'rr.id_review = r.id_review');
        $sql->where('rr.id_criteria = ' . (int)$id_criteria);
        $sql->where('r.type = "' . pSQL($type) . '"');
        $sql->where('r.status = "approved"');

        if ($id_product && $type === 'product') {
            $sql->where('r.id_product = ' . (int)$id_product);
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }
}
