{**
 * Product Reviews Hook Template
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 *}

<div id="product-reviews" class="product-reviews">
  
  {* Reviews Summary *}
  {if $rating_stats && $rating_stats.total_reviews > 0}
    <div class="reviews-summary">
      <div class="row">
        <div class="col-md-4">
          <div class="overall-rating">
            <div class="rating-number">{$rating_stats.average_rating|string_format:"%.1f"}</div>
            <div class="rating-stars">
              {for $i=1 to 5}
                {if $i <= $rating_stats.average_rating}
                  <i class="material-icons star filled" style="color: {$star_color};">star</i>
                {else}
                  <i class="material-icons star" style="color: #ddd;">star_border</i>
                {/if}
              {/for}
            </div>
            <div class="rating-count">
              {$rating_stats.total_reviews} {if $rating_stats.total_reviews == 1}{l s='review' mod='streviewmaneger'}{else}{l s='reviews' mod='streviewmaneger'}{/if}
            </div>
          </div>
        </div>
        
        <div class="col-md-8">
          <div class="rating-breakdown">
            {for $i=5 to 1 step -1}
              {assign var="star_count" value=""}
              {if $i == 5}{assign var="star_count" value=$rating_stats.five_stars}
              {elseif $i == 4}{assign var="star_count" value=$rating_stats.four_stars}
              {elseif $i == 3}{assign var="star_count" value=$rating_stats.three_stars}
              {elseif $i == 2}{assign var="star_count" value=$rating_stats.two_stars}
              {elseif $i == 1}{assign var="star_count" value=$rating_stats.one_star}
              {/if}
              
              <div class="rating-bar">
                <span class="star-label">{$i} <i class="material-icons" style="color: {$star_color}; font-size: 14px;">star</i></span>
                <div class="progress">
                  <div class="progress-bar" style="width: {if $rating_stats.total_reviews > 0}{($star_count / $rating_stats.total_reviews * 100)|string_format:"%.0f"}{else}0{/if}%"></div>
                </div>
                <span class="count">({$star_count})</span>
              </div>
            {/for}
          </div>
        </div>
      </div>
    </div>

    {* Criteria Averages *}
    {if $criteria_averages}
      <div class="criteria-averages">
        <h4>{l s='Rating Breakdown' mod='streviewmaneger'}</h4>
        <div class="row">
          {foreach $criteria_averages as $criteria}
            <div class="col-md-6">
              <div class="criteria-item">
                <div class="criteria-name">{$criteria.criteria_name}</div>
                <div class="criteria-rating">
                  <div class="stars">
                    {for $i=1 to 5}
                      {if $i <= $criteria.average_rating}
                        <i class="material-icons star filled" style="color: {$star_color};">star</i>
                      {else}
                        <i class="material-icons star" style="color: #ddd;">star_border</i>
                      {/if}
                    {/for}
                  </div>
                  <span class="rating-value">{$criteria.average_rating|string_format:"%.1f"}</span>
                </div>
              </div>
            </div>
          {/foreach}
        </div>
      </div>
    {/if}
  {/if}

  {* Write Review Button *}
  <div class="write-review-section">
    {if $customer_logged}
      {if $can_review}
        <a href="{$review_form_url}" class="btn btn-primary btn-write-review">
          <i class="material-icons">rate_review</i>
          {l s='Write a Review' mod='streviewmaneger'}
        </a>
      {else}
        <p class="text-muted">
          <i class="material-icons">info</i>
          {l s='You have already reviewed this product or you need to purchase it first.' mod='streviewmaneger'}
        </p>
      {/if}
    {else}
      <a href="{$login_url}?back={$review_form_url|urlencode}" class="btn btn-outline-primary">
        <i class="material-icons">login</i>
        {l s='Login to Write a Review' mod='streviewmaneger'}
      </a>
    {/if}
  </div>

  {* Reviews List *}
  {if $reviews}
    <div class="reviews-list">
      <h4>{l s='Customer Reviews' mod='streviewmaneger'}</h4>
      
      {foreach $reviews as $review}
        <div class="review-item" data-review-id="{$review.id_review}">
          <div class="review-header">
            <div class="reviewer-info">
              <strong class="reviewer-name">{$review.firstname} {$review.lastname|substr:0:1}.</strong>
              {if $verified_badge_enabled && $review.verified_purchase}
                <span class="verified-badge" style="color: {$verified_badge_color};">
                  <i class="material-icons">verified</i>
                  {$verified_badge_text}
                </span>
              {/if}
            </div>
            <div class="review-date">{$review.date_add|date_format:"%B %d, %Y"}</div>
          </div>

          <div class="review-rating">
            <div class="overall-stars">
              {for $i=1 to 5}
                {if $i <= $review.overall_rating}
                  <i class="material-icons star filled" style="color: {$star_color};">star</i>
                {else}
                  <i class="material-icons star" style="color: #ddd;">star_border</i>
                {/if}
              {/for}
            </div>
            
            {if $review.ratings}
              <div class="individual-ratings">
                {foreach $review.ratings as $rating}
                  <div class="rating-criteria">
                    <span class="criteria-name">{$rating.criteria_name}:</span>
                    <div class="criteria-stars">
                      {for $i=1 to 5}
                        {if $i <= $rating.rating}
                          <i class="material-icons star mini filled" style="color: {$star_color};">star</i>
                        {else}
                          <i class="material-icons star mini" style="color: #ddd;">star_border</i>
                        {/if}
                      {/for}
                    </div>
                  </div>
                {/foreach}
              </div>
            {/if}
          </div>

          <div class="review-content">
            <h5 class="review-title">{$review.title}</h5>
            <p class="review-text">{$review.content|nl2br}</p>
          </div>

          {* Merchant Reply *}
          {if $review.reply}
            <div class="merchant-reply">
              <div class="reply-header">
                <strong>{l s='Response from' mod='streviewmaneger'} {$review.reply.firstname} {$review.reply.lastname}</strong>
                <span class="reply-date">{$review.reply.date_add|date_format:"%B %d, %Y"}</span>
              </div>
              <div class="reply-content">
                {$review.reply.content|nl2br}
              </div>
            </div>
          {/if}

          {* Helpful Votes *}
          <div class="review-actions">
            <div class="helpful-votes">
              <span class="helpful-label">{l s='Was this review helpful?' mod='streviewmaneger'}</span>
              <button type="button" class="btn btn-sm btn-outline-success helpful-btn" data-helpful="1">
                <i class="material-icons">thumb_up</i>
                {l s='Yes' mod='streviewmaneger'} ({$review.helpful_yes})
              </button>
              <button type="button" class="btn btn-sm btn-outline-danger helpful-btn" data-helpful="0">
                <i class="material-icons">thumb_down</i>
                {l s='No' mod='streviewmaneger'} ({$review.helpful_no})
              </button>
            </div>

            {* Social Sharing *}
            {if $social_sharing_enabled}
              <div class="social-sharing">
                {if $facebook_sharing}
                  <a href="#" class="btn btn-sm btn-facebook share-btn" data-platform="facebook">
                    <i class="fab fa-facebook-f"></i>
                  </a>
                {/if}
                {if $twitter_sharing}
                  <a href="#" class="btn btn-sm btn-twitter share-btn" data-platform="twitter">
                    <i class="fab fa-twitter"></i>
                  </a>
                {/if}
              </div>
            {/if}

            {* Report/Dispute *}
            <div class="review-report">
              <button type="button" class="btn btn-sm btn-link text-muted report-btn">
                <i class="material-icons">flag</i>
                {l s='Report' mod='streviewmaneger'}
              </button>
            </div>
          </div>
        </div>
      {/foreach}
    </div>

    {* Load More Button *}
    <div class="load-more-section text-center">
      <button type="button" class="btn btn-outline-primary" id="load-more-reviews">
        {l s='Load More Reviews' mod='streviewmaneger'}
      </button>
    </div>
  {else}
    <div class="no-reviews">
      <div class="text-center">
        <i class="material-icons" style="font-size: 48px; color: #ddd;">rate_review</i>
        <h4>{l s='No reviews yet' mod='streviewmaneger'}</h4>
        <p class="text-muted">{l s='Be the first to review this product!' mod='streviewmaneger'}</p>
        {if $customer_logged && $can_review}
          <a href="{$review_form_url}" class="btn btn-primary">
            <i class="material-icons">rate_review</i>
            {l s='Write the First Review' mod='streviewmaneger'}
          </a>
        {/if}
      </div>
    </div>
  {/if}
</div>

{* Report Modal *}
<div class="modal fade" id="report-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{l s='Report Review' mod='streviewmaneger'}</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="report-form">
          <input type="hidden" id="report-review-id" name="id_review">
          
          <div class="form-group">
            <label for="report-reason">{l s='Reason for reporting' mod='streviewmaneger'}:</label>
            <select id="report-reason" name="reason" class="form-control" required>
              <option value="">{l s='Select a reason' mod='streviewmaneger'}</option>
              <option value="inappropriate_content">{l s='Inappropriate Content' mod='streviewmaneger'}</option>
              <option value="fake_review">{l s='Fake Review' mod='streviewmaneger'}</option>
              <option value="spam">{l s='Spam' mod='streviewmaneger'}</option>
              <option value="personal_information">{l s='Contains Personal Information' mod='streviewmaneger'}</option>
              <option value="off_topic">{l s='Off Topic' mod='streviewmaneger'}</option>
              <option value="other">{l s='Other' mod='streviewmaneger'}</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="report-description">{l s='Additional details' mod='streviewmaneger'}:</label>
            <textarea id="report-description" name="description" class="form-control" rows="3" required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">{l s='Cancel' mod='streviewmaneger'}</button>
        <button type="button" class="btn btn-danger" id="submit-report">{l s='Submit Report' mod='streviewmaneger'}</button>
      </div>
    </div>
  </div>
</div>
