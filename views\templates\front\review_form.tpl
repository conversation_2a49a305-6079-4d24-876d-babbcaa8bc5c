{**
 * Review Form Template
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 *}

{extends file='page.tpl'}

{block name='page_title'}
  {if $type == 'product'}
    {l s='Review Product' mod='streviewmaneger'}: {$product->name}
  {else}
    {l s='Review Our Store' mod='streviewmaneger'}
  {/if}
{/block}

{block name='page_content'}
  <div class="review-form-container">
    
    {if $errors}
      <div class="alert alert-danger">
        <ul class="mb-0">
          {foreach $errors as $error}
            <li>{$error}</li>
          {/foreach}
        </ul>
      </div>
    {/if}

    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          {if $type == 'product'}
            <i class="material-icons">star</i>
            {l s='Rate this product' mod='streviewmaneger'}
          {else}
            <i class="material-icons">store</i>
            {l s='Rate your experience' mod='streviewmaneger'}
          {/if}
        </h3>
      </div>
      
      <div class="card-body">
        <form id="review-form" action="{$action_url}" method="post">
          <input type="hidden" name="type" value="{$type}">
          {if $type == 'product'}
            <input type="hidden" name="id_product" value="{$product->id}">
          {/if}
          
          {* Review Title *}
          <div class="form-group">
            <label for="review-title" class="form-label required">
              {l s='Review Title' mod='streviewmaneger'}
            </label>
            <input type="text" 
                   id="review-title" 
                   name="title" 
                   class="form-control" 
                   placeholder="{l s='Give your review a title' mod='streviewmaneger'}" 
                   required>
          </div>

          {* Rating Criteria *}
          {if $criteria}
            <div class="form-group">
              <label class="form-label required">
                {l s='Rate the following criteria' mod='streviewmaneger'}
              </label>
              
              {foreach $criteria as $criterion}
                <div class="criteria-rating mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="criteria-name">{$criterion.name}</span>
                    <span class="rating-value">0</span>
                  </div>
                  
                  <div class="star-rating" data-criteria="{$criterion.id_criteria}">
                    {for $i=1 to 5}
                      <i class="star material-icons" data-rating="{$i}">star_border</i>
                    {/for}
                  </div>
                  
                  <input type="hidden" 
                         name="ratings[{$criterion.id_criteria}]" 
                         class="rating-input" 
                         required>
                </div>
              {/foreach}
            </div>
          {/if}

          {* Review Content *}
          <div class="form-group">
            <label for="review-content" class="form-label required">
              {l s='Your Review' mod='streviewmaneger'}
            </label>
            <textarea id="review-content" 
                      name="content" 
                      class="form-control" 
                      rows="5" 
                      placeholder="{l s='Share your experience...' mod='streviewmaneger'}" 
                      required></textarea>
            <small class="form-text text-muted">
              {l s='Minimum 10 characters required' mod='streviewmaneger'}
            </small>
          </div>

          {* Submit Button *}
          <div class="form-group text-center">
            <button type="submit" class="btn btn-primary btn-lg">
              <i class="material-icons">send</i>
              {l s='Submit Review' mod='streviewmaneger'}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  {* Success Modal *}
  <div class="modal fade" id="success-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="material-icons text-success">check_circle</i>
            {l s='Thank You!' mod='streviewmaneger'}
          </h5>
        </div>
        <div class="modal-body">
          <p id="success-message"></p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-dismiss="modal">
            {l s='Close' mod='streviewmaneger'}
          </button>
        </div>
      </div>
    </div>
  </div>
{/block}

{block name='page_footer'}
  <style>
    .review-form-container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .criteria-rating {
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: #f9f9f9;
    }
    
    .criteria-name {
      font-weight: 600;
      color: #333;
    }
    
    .rating-value {
      font-weight: bold;
      color: #ff6b35;
    }
    
    .star-rating {
      display: flex;
      gap: 5px;
    }
    
    .star {
      cursor: pointer;
      font-size: 24px;
      color: #ddd;
      transition: color 0.2s ease;
    }
    
    .star:hover,
    .star.active {
      color: #ffd700;
    }
    
    .star.filled {
      color: #ffd700;
    }
    
    .form-label.required::after {
      content: " *";
      color: #e74c3c;
    }
    
    .btn-primary {
      background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
      border: none;
      padding: 12px 30px;
      border-radius: 25px;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Star rating functionality
      const starRatings = document.querySelectorAll('.star-rating');
      
      starRatings.forEach(function(rating) {
        const stars = rating.querySelectorAll('.star');
        const criteriaId = rating.dataset.criteria;
        const input = document.querySelector(`input[name="ratings[${criteriaId}]"]`);
        const valueDisplay = rating.parentElement.querySelector('.rating-value');
        
        stars.forEach(function(star, index) {
          star.addEventListener('click', function() {
            const ratingValue = index + 1;
            input.value = ratingValue;
            valueDisplay.textContent = ratingValue;
            
            // Update star display
            stars.forEach(function(s, i) {
              if (i <= index) {
                s.classList.add('filled');
                s.textContent = 'star';
              } else {
                s.classList.remove('filled');
                s.textContent = 'star_border';
              }
            });
          });
          
          star.addEventListener('mouseenter', function() {
            stars.forEach(function(s, i) {
              if (i <= index) {
                s.classList.add('active');
              } else {
                s.classList.remove('active');
              }
            });
          });
        });
        
        rating.addEventListener('mouseleave', function() {
          stars.forEach(function(s) {
            s.classList.remove('active');
          });
        });
      });
      
      // Form submission
      const form = document.getElementById('review-form');
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate content length
        const content = document.getElementById('review-content').value;
        if (content.length < 10) {
          alert('{l s="Review content must be at least 10 characters long." mod="streviewmaneger"}');
          return;
        }
        
        // Check if all ratings are provided
        const ratingInputs = document.querySelectorAll('.rating-input');
        let allRated = true;
        ratingInputs.forEach(function(input) {
          if (!input.value) {
            allRated = false;
          }
        });
        
        if (!allRated) {
          alert('{l s="Please rate all criteria." mod="streviewmaneger"}');
          return;
        }
        
        // Submit form via AJAX
        const formData = new FormData(form);
        
        fetch(form.action, {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            document.getElementById('success-message').textContent = data.message;
            $('#success-modal').modal('show');
            form.reset();
            
            // Reset star ratings
            starRatings.forEach(function(rating) {
              const stars = rating.querySelectorAll('.star');
              const valueDisplay = rating.parentElement.querySelector('.rating-value');
              valueDisplay.textContent = '0';
              stars.forEach(function(star) {
                star.classList.remove('filled');
                star.textContent = 'star_border';
              });
            });
          } else {
            alert(data.error || '{l s="An error occurred. Please try again." mod="streviewmaneger"}');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('{l s="An error occurred. Please try again." mod="streviewmaneger"}');
        });
      });
    });
  </script>
{/block}
