<?php
/**
 * Admin Review Configuration Controller
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStReviewConfigController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->display = 'view';
        
        parent::__construct();
    }

    public function initContent()
    {
        $this->initTabModuleList();
        $this->initToolbar();
        $this->initPageHeaderToolbar();

        if (Tools::isSubmit('submitStReviewConfig')) {
            $this->processConfiguration();
        }

        $this->content .= $this->renderConfigurationForm();
        
        $this->context->smarty->assign([
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ]);
    }

    public function renderConfigurationForm()
    {
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'st_review_config';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = 'id_configuration';
        $helper->submit_action = 'submitStReviewConfig';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminStReviewConfig', false);
        $helper->token = Tools::getAdminTokenLite('AdminStReviewConfig');

        $helper->tpl_vars = [
            'fields_value' => $this->getConfigFieldsValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm($this->getConfigForm());
    }

    public function getConfigForm()
    {
        return [
            [
                'form' => [
                    'legend' => [
                        'title' => $this->l('General Settings'),
                        'icon' => 'icon-cogs'
                    ],
                    'input' => [
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Product Reviews'),
                            'name' => 'product_reviews_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Store Reviews'),
                            'name' => 'store_reviews_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Moderation'),
                            'name' => 'moderation_enabled',
                            'desc' => $this->l('Reviews will require approval before being displayed'),
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Auto-approve Verified Purchases'),
                            'name' => 'auto_approve_verified',
                            'desc' => $this->l('Automatically approve reviews from verified purchases'),
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ]
                    ],
                    'submit' => [
                        'title' => $this->l('Save'),
                        'class' => 'btn btn-default pull-right'
                    ]
                ]
            ],
            [
                'form' => [
                    'legend' => [
                        'title' => $this->l('Email Settings'),
                        'icon' => 'icon-envelope'
                    ],
                    'input' => [
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Email Requests'),
                            'name' => 'email_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'text',
                            'label' => $this->l('Email Delay (days)'),
                            'name' => 'email_delay_days',
                            'class' => 'fixed-width-sm',
                            'desc' => $this->l('Number of days after order completion to send review request')
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Reminders'),
                            'name' => 'reminder_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'text',
                            'label' => $this->l('Reminder Delay (days)'),
                            'name' => 'reminder_delay_days',
                            'class' => 'fixed-width-sm',
                            'desc' => $this->l('Number of days after first email to send reminder')
                        ]
                    ],
                    'submit' => [
                        'title' => $this->l('Save'),
                        'class' => 'btn btn-default pull-right'
                    ]
                ]
            ],
            [
                'form' => [
                    'legend' => [
                        'title' => $this->l('Display Settings'),
                        'icon' => 'icon-eye'
                    ],
                    'input' => [
                        [
                            'type' => 'color',
                            'label' => $this->l('Star Color'),
                            'name' => 'star_color',
                            'class' => 'fixed-width-sm'
                        ],
                        [
                            'type' => 'text',
                            'label' => $this->l('Star Size (px)'),
                            'name' => 'star_size',
                            'class' => 'fixed-width-sm'
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Show Verified Badge'),
                            'name' => 'verified_badge_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'text',
                            'label' => $this->l('Verified Badge Text'),
                            'name' => 'verified_badge_text',
                            'lang' => true
                        ],
                        [
                            'type' => 'color',
                            'label' => $this->l('Verified Badge Color'),
                            'name' => 'verified_badge_color',
                            'class' => 'fixed-width-sm'
                        ]
                    ],
                    'submit' => [
                        'title' => $this->l('Save'),
                        'class' => 'btn btn-default pull-right'
                    ]
                ]
            ],
            [
                'form' => [
                    'legend' => [
                        'title' => $this->l('Social Sharing'),
                        'icon' => 'icon-share'
                    ],
                    'input' => [
                        [
                            'type' => 'switch',
                            'label' => $this->l('Enable Social Sharing'),
                            'name' => 'social_sharing_enabled',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Facebook Sharing'),
                            'name' => 'facebook_sharing',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ],
                        [
                            'type' => 'switch',
                            'label' => $this->l('Twitter Sharing'),
                            'name' => 'twitter_sharing',
                            'values' => [
                                ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                                ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                            ]
                        ]
                    ],
                    'submit' => [
                        'title' => $this->l('Save'),
                        'class' => 'btn btn-default pull-right'
                    ]
                ]
            ]
        ];
    }

    public function getConfigFieldsValues()
    {
        $config_values = [];
        $default_config = StReviewConfig::getDefaultConfig();

        foreach ($default_config as $key => $default_value) {
            $config_values[$key] = StReviewConfig::get($key, $default_value);
        }

        return $config_values;
    }

    public function processConfiguration()
    {
        $config_keys = [
            'product_reviews_enabled',
            'store_reviews_enabled',
            'moderation_enabled',
            'auto_approve_verified',
            'email_enabled',
            'email_delay_days',
            'reminder_enabled',
            'reminder_delay_days',
            'star_color',
            'star_size',
            'verified_badge_enabled',
            'verified_badge_text',
            'verified_badge_color',
            'social_sharing_enabled',
            'facebook_sharing',
            'twitter_sharing'
        ];

        $errors = [];
        
        foreach ($config_keys as $key) {
            $value = Tools::getValue($key);
            
            // Validate configuration values
            if (!StReviewConfig::validateConfig($key, $value)) {
                $errors[] = sprintf($this->l('Invalid value for %s'), $key);
                continue;
            }

            if (!StReviewConfig::set($key, $value)) {
                $errors[] = sprintf($this->l('Could not save %s'), $key);
            }
        }

        if (empty($errors)) {
            $this->confirmations[] = $this->l('Configuration saved successfully.');
        } else {
            $this->errors = array_merge($this->errors, $errors);
        }
    }
}
