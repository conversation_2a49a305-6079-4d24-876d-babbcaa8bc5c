<?php
/**
 * Admin Reviews Controller
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStReviewsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'st_reviews';
        $this->className = 'StReview';
        $this->identifier = 'id_review';
        $this->bootstrap = true;
        $this->lang = false;

        parent::__construct();

        $this->fields_list = [
            'id_review' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'type' => [
                'title' => $this->l('Type'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'type' => 'select',
                'list' => [
                    'product' => $this->l('Product'),
                    'store' => $this->l('Store')
                ],
                'filter_key' => 'a!type'
            ],
            'title' => [
                'title' => $this->l('Title'),
                'width' => 'auto'
            ],
            'customer_name' => [
                'title' => $this->l('Customer'),
                'width' => 'auto',
                'search' => false
            ],
            'product_name' => [
                'title' => $this->l('Product'),
                'width' => 'auto',
                'search' => false
            ],
            'overall_rating' => [
                'title' => $this->l('Rating'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'callback' => 'displayRating'
            ],
            'status' => [
                'title' => $this->l('Status'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'type' => 'select',
                'list' => [
                    'pending' => $this->l('Pending'),
                    'approved' => $this->l('Approved'),
                    'rejected' => $this->l('Rejected')
                ],
                'filter_key' => 'a!status',
                'callback' => 'displayStatus'
            ],
            'verified_purchase' => [
                'title' => $this->l('Verified'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'type' => 'bool',
                'callback' => 'displayVerified'
            ],
            'date_add' => [
                'title' => $this->l('Date'),
                'align' => 'center',
                'class' => 'fixed-width-lg',
                'type' => 'datetime'
            ]
        ];

        $this->bulk_actions = [
            'approve' => [
                'text' => $this->l('Approve'),
                'icon' => 'icon-check',
                'confirm' => $this->l('Approve selected reviews?')
            ],
            'reject' => [
                'text' => $this->l('Reject'),
                'icon' => 'icon-remove',
                'confirm' => $this->l('Reject selected reviews?')
            ],
            'delete' => [
                'text' => $this->l('Delete'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected reviews?')
            ]
        ];

        $this->actions = ['view', 'edit', 'delete'];
    }

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_review'] = [
                'href' => self::$currentIndex . '&addst_reviews&token=' . $this->token,
                'desc' => $this->l('Add new review'),
                'icon' => 'process-icon-new'
            ];
        }

        parent::initPageHeaderToolbar();
    }

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        $this->addRowAction('reply');
        $this->addRowAction('approve');
        $this->addRowAction('reject');

        $this->_select = '
            CONCAT(c.firstname, " ", c.lastname) as customer_name,
            pl.name as product_name';

        $this->_join = '
            LEFT JOIN ' . _DB_PREFIX_ . 'customer c ON (a.id_customer = c.id_customer)
            LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (a.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')';

        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);
    }

    public function displayRating($value, $row)
    {
        $stars = '';
        $rating = (float)$value;
        
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $stars .= '<i class="icon-star" style="color: #FFD700;"></i>';
            } else {
                $stars .= '<i class="icon-star-o" style="color: #ccc;"></i>';
            }
        }
        
        return $stars . ' (' . number_format($rating, 1) . ')';
    }

    public function displayStatus($value, $row)
    {
        $status_colors = [
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger'
        ];

        $color = isset($status_colors[$value]) ? $status_colors[$value] : 'default';
        return '<span class="badge badge-' . $color . '">' . ucfirst($value) . '</span>';
    }

    public function displayVerified($value, $row)
    {
        return $value ? '<i class="icon-check text-success"></i>' : '<i class="icon-remove text-danger"></i>';
    }

    public function displayReplyLink($token, $id, $name = null)
    {
        $tpl = $this->createTemplate('helpers/list/list_action_reply.tpl');
        $tpl->assign([
            'href' => self::$currentIndex . '&' . $this->identifier . '=' . $id . '&reply' . $this->table . '&token=' . ($token != null ? $token : $this->token),
            'action' => $this->l('Reply'),
            'id' => $id
        ]);

        return $tpl->fetch();
    }

    public function displayApproveLink($token, $id, $name = null)
    {
        $tpl = $this->createTemplate('helpers/list/list_action_approve.tpl');
        $tpl->assign([
            'href' => self::$currentIndex . '&' . $this->identifier . '=' . $id . '&approve' . $this->table . '&token=' . ($token != null ? $token : $this->token),
            'action' => $this->l('Approve'),
            'id' => $id
        ]);

        return $tpl->fetch();
    }

    public function displayRejectLink($token, $id, $name = null)
    {
        $tpl = $this->createTemplate('helpers/list/list_action_reject.tpl');
        $tpl->assign([
            'href' => self::$currentIndex . '&' . $this->identifier . '=' . $id . '&reject' . $this->table . '&token=' . ($token != null ? $token : $this->token),
            'action' => $this->l('Reject'),
            'id' => $id
        ]);

        return $tpl->fetch();
    }

    public function processApprove()
    {
        $review = new StReview((int)Tools::getValue('id_review'));
        if (Validate::isLoadedObject($review)) {
            if ($review->approve()) {
                $this->confirmations[] = $this->l('Review approved successfully.');
            } else {
                $this->errors[] = $this->l('Error occurred while approving review.');
            }
        } else {
            $this->errors[] = $this->l('Review not found.');
        }
    }

    public function processReject()
    {
        $review = new StReview((int)Tools::getValue('id_review'));
        if (Validate::isLoadedObject($review)) {
            if ($review->reject()) {
                $this->confirmations[] = $this->l('Review rejected successfully.');
            } else {
                $this->errors[] = $this->l('Error occurred while rejecting review.');
            }
        } else {
            $this->errors[] = $this->l('Review not found.');
        }
    }

    public function processBulkApprove()
    {
        $reviews = Tools::getValue($this->table . 'Box');
        if (is_array($reviews) && count($reviews)) {
            $success = 0;
            foreach ($reviews as $id_review) {
                $review = new StReview((int)$id_review);
                if (Validate::isLoadedObject($review) && $review->approve()) {
                    $success++;
                }
            }
            $this->confirmations[] = sprintf($this->l('%d reviews approved successfully.'), $success);
        }
    }

    public function processBulkReject()
    {
        $reviews = Tools::getValue($this->table . 'Box');
        if (is_array($reviews) && count($reviews)) {
            $success = 0;
            foreach ($reviews as $id_review) {
                $review = new StReview((int)$id_review);
                if (Validate::isLoadedObject($review) && $review->reject()) {
                    $success++;
                }
            }
            $this->confirmations[] = sprintf($this->l('%d reviews rejected successfully.'), $success);
        }
    }

    public function renderForm()
    {
        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Review'),
                'icon' => 'icon-star'
            ],
            'input' => [
                [
                    'type' => 'select',
                    'label' => $this->l('Type'),
                    'name' => 'type',
                    'required' => true,
                    'options' => [
                        'query' => [
                            ['id' => 'product', 'name' => $this->l('Product Review')],
                            ['id' => 'store', 'name' => $this->l('Store Review')]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Customer'),
                    'name' => 'id_customer',
                    'required' => true,
                    'options' => [
                        'query' => Customer::getCustomers(),
                        'id' => 'id_customer',
                        'name' => 'email'
                    ]
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'required' => true,
                    'size' => 50
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Content'),
                    'name' => 'content',
                    'required' => true,
                    'rows' => 5,
                    'cols' => 50
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Overall Rating'),
                    'name' => 'overall_rating',
                    'required' => true,
                    'class' => 'fixed-width-sm',
                    'desc' => $this->l('Rating from 1 to 5')
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Status'),
                    'name' => 'status',
                    'required' => true,
                    'options' => [
                        'query' => [
                            ['id' => 'pending', 'name' => $this->l('Pending')],
                            ['id' => 'approved', 'name' => $this->l('Approved')],
                            ['id' => 'rejected', 'name' => $this->l('Rejected')]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Verified Purchase'),
                    'name' => 'verified_purchase',
                    'values' => [
                        [
                            'id' => 'verified_on',
                            'value' => 1,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'verified_off',
                            'value' => 0,
                            'label' => $this->l('No')
                        ]
                    ]
                ]
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        return parent::renderForm();
    }

    public function renderView()
    {
        $review = new StReview((int)Tools::getValue('id_review'));
        if (!Validate::isLoadedObject($review)) {
            $this->errors[] = $this->l('Review not found.');
            return;
        }

        // Get customer info
        $customer = new Customer($review->id_customer);
        
        // Get product info if product review
        $product = null;
        if ($review->type === 'product' && $review->id_product) {
            $product = new Product($review->id_product, false, $this->context->language->id);
        }

        // Get ratings
        $ratings = StReviewRating::getRatingsByReview($review->id);

        // Get reply
        $reply = StReviewReply::getReplyByReview($review->id);

        $this->context->smarty->assign([
            'review' => $review,
            'customer' => $customer,
            'product' => $product,
            'ratings' => $ratings,
            'reply' => $reply,
            'current_index' => self::$currentIndex,
            'token' => $this->token
        ]);

        return $this->context->smarty->fetch($this->module->getLocalPath() . 'views/templates/admin/review_view.tpl');
    }
}
