<?php
/**
 * CRON Controller for Email Queue Processing
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StreviewmanegerCronModuleFrontController extends ModuleFrontController
{
    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
    }

    public function initContent()
    {
        // Security check - only allow CRON access with proper token
        $token = Tools::getValue('token');
        $expected_token = Configuration::get('STREVIEW_CRON_TOKEN');
        
        if (!$expected_token) {
            // Generate and save token if not exists
            $expected_token = Tools::passwdGen(32);
            Configuration::updateValue('STREVIEW_CRON_TOKEN', $expected_token);
        }

        if ($token !== $expected_token) {
            http_response_code(403);
            die('Access denied');
        }

        $action = Tools::getValue('action', 'process_queue');
        
        switch ($action) {
            case 'process_queue':
                $this->processEmailQueue();
                break;
            case 'add_orders':
                $this->addOrdersToQueue();
                break;
            case 'cleanup':
                $this->cleanupOldData();
                break;
            case 'stats':
                $this->displayStats();
                break;
            default:
                $this->processEmailQueue();
                break;
        }
    }

    public function processEmailQueue()
    {
        $start_time = microtime(true);
        $cron_enabled = StReviewConfig::get('cron_enabled', true);
        
        if (!$cron_enabled) {
            $this->outputResult(['status' => 'disabled', 'message' => 'CRON processing is disabled']);
            return;
        }

        $batch_size = (int)StReviewConfig::get('cron_batch_size', 50);
        $results = StReviewEmailQueue::processQueue($batch_size);
        
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        $this->outputResult([
            'status' => 'success',
            'sent' => $results['sent'],
            'failed' => $results['failed'],
            'errors' => $results['errors'],
            'execution_time_ms' => $execution_time,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    public function addOrdersToQueue()
    {
        $start_time = microtime(true);
        $email_enabled = StReviewConfig::get('email_enabled', true);
        
        if (!$email_enabled) {
            $this->outputResult(['status' => 'disabled', 'message' => 'Email requests are disabled']);
            return;
        }

        $delay_days = (int)StReviewConfig::get('email_delay_days', 7);
        $order_statuses = StReviewConfig::get('email_order_statuses', [2, 3, 4]); // Paid, Preparation, Shipped
        
        if (!is_array($order_statuses)) {
            $order_statuses = [2, 3, 4];
        }

        // Get orders that should have review requests sent
        $sql = new DbQuery();
        $sql->select('DISTINCT o.id_order, o.id_customer');
        $sql->from('orders', 'o');
        $sql->where('o.current_state IN (' . implode(',', array_map('intval', $order_statuses)) . ')');
        $sql->where('DATE(o.date_add) = DATE(DATE_SUB(NOW(), INTERVAL ' . $delay_days . ' DAY))');
        
        // Exclude orders that already have emails in queue
        $sql->where('o.id_order NOT IN (
            SELECT DISTINCT id_order 
            FROM ' . _DB_PREFIX_ . 'st_review_email_queue 
            WHERE email_type = "first_request"
        )');

        $orders = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        $added_product = 0;
        $added_store = 0;
        $skipped = 0;

        foreach ($orders as $order_data) {
            $id_customer = (int)$order_data['id_customer'];
            $id_order = (int)$order_data['id_order'];

            // Check if customer is excluded
            if (StReviewExclusion::isCustomerExcluded($id_customer)) {
                $skipped++;
                continue;
            }

            // Add product review requests if enabled
            $product_reviews_enabled = StReviewConfig::get('product_reviews_enabled', true);
            if ($product_reviews_enabled) {
                if (StReviewEmailQueue::addToQueue($id_customer, $id_order, 'first_request', 'product', 0)) {
                    $added_product++;
                }
            }

            // Add store review request if enabled
            $store_reviews_enabled = StReviewConfig::get('store_reviews_enabled', true);
            if ($store_reviews_enabled) {
                if (StReviewEmailQueue::addToQueue($id_customer, $id_order, 'first_request', 'store', 0)) {
                    $added_store++;
                }
            }
        }

        $execution_time = round((microtime(true) - $start_time) * 1000, 2);

        $this->outputResult([
            'status' => 'success',
            'orders_processed' => count($orders),
            'product_emails_added' => $added_product,
            'store_emails_added' => $added_store,
            'skipped' => $skipped,
            'execution_time_ms' => $execution_time,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    public function cleanupOldData()
    {
        $start_time = microtime(true);
        $results = [];

        // Clean old email queue items (90 days)
        $queue_cleaned = StReviewEmailQueue::cleanOldItems(90);
        $results['email_queue_cleaned'] = $queue_cleaned;

        // Clean inactive exclusions (365 days)
        $exclusions_cleaned = StReviewExclusion::cleanInactiveExclusions(365);
        $results['exclusions_cleaned'] = $exclusions_cleaned;

        // Clean old dispute notifications (30 days for resolved/rejected)
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_disputes` 
                WHERE `status` IN ("resolved", "rejected") 
                AND `date_upd` < DATE_SUB(NOW(), INTERVAL 30 DAY)';
        $disputes_cleaned = Db::getInstance()->execute($sql);
        $results['disputes_cleaned'] = $disputes_cleaned;

        $execution_time = round((microtime(true) - $start_time) * 1000, 2);

        $this->outputResult([
            'status' => 'success',
            'results' => $results,
            'execution_time_ms' => $execution_time,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    public function displayStats()
    {
        $stats = [];

        // Email queue stats
        $stats['email_queue'] = StReviewEmailQueue::getQueueStats(30);

        // Review stats
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_reviews,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_reviews,
            SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_reviews,
            SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_reviews,
            AVG(overall_rating) as average_rating
        ');
        $sql->from('st_reviews');
        $sql->where('date_add >= DATE_SUB(NOW(), INTERVAL 30 DAY)');

        $stats['reviews'] = Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);

        // Dispute stats
        $stats['disputes'] = StReviewDispute::getDisputeStats(30);

        // Reply stats
        $stats['replies'] = StReviewReply::getReplyStats(30);

        // Exclusion stats
        $stats['exclusions'] = StReviewExclusion::getExclusionStats();

        $this->outputResult([
            'status' => 'success',
            'stats' => $stats,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    private function outputResult($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit;
    }

    public function postProcess()
    {
        // Override to prevent default processing
    }

    public function display()
    {
        // Override to prevent default display
    }
}

/**
 * CRON Setup Instructions:
 * 
 * Add these CRON jobs to your server:
 * 
 * # Process email queue every hour
 * 0 * * * * curl -s "https://yourstore.com/module/streviewmaneger/cron?token=YOUR_TOKEN&action=process_queue" > /dev/null
 * 
 * # Add new orders to queue daily at 9 AM
 * 0 9 * * * curl -s "https://yourstore.com/module/streviewmaneger/cron?token=YOUR_TOKEN&action=add_orders" > /dev/null
 * 
 * # Cleanup old data weekly on Sunday at 2 AM
 * 0 2 * * 0 curl -s "https://yourstore.com/module/streviewmaneger/cron?token=YOUR_TOKEN&action=cleanup" > /dev/null
 * 
 * Replace YOUR_TOKEN with the actual token from Configuration::get('STREVIEW_CRON_TOKEN')
 * Replace yourstore.com with your actual domain
 */
