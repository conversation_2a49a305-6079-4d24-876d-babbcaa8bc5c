<?php
/**
 * Review Configuration Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewConfig extends ObjectModel
{
    /** @var string Name */
    public $name;

    /** @var string Value */
    public $value;

    /** @var int Shop ID */
    public $id_shop;

    /** @var int Shop Group ID */
    public $id_shop_group;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_config',
        'primary' => 'id_config',
        'fields' => [
            'name' => ['type' => self::TYPE_STRING, 'validate' => 'isConfigName', 'required' => true, 'size' => 255],
            'value' => ['type' => self::TYPE_STRING, 'validate' => 'isString'],
            'id_shop' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'id_shop_group' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get configuration value
     *
     * @param string $name
     * @param mixed $default
     * @param int $id_shop
     * @param int $id_shop_group
     * @return mixed
     */
    public static function get($name, $default = null, $id_shop = null, $id_shop_group = null)
    {
        if ($id_shop === null) {
            $id_shop = Context::getContext()->shop->id;
        }
        
        if ($id_shop_group === null) {
            $id_shop_group = Context::getContext()->shop->id_shop_group;
        }

        $sql = new DbQuery();
        $sql->select('value');
        $sql->from('st_review_config');
        $sql->where('name = "' . pSQL($name) . '"');
        
        // Try shop-specific first
        $sql->where('(id_shop = ' . (int)$id_shop . ' OR id_shop IS NULL)');
        $sql->where('(id_shop_group = ' . (int)$id_shop_group . ' OR id_shop_group IS NULL)');
        $sql->orderBy('id_shop DESC, id_shop_group DESC');
        $sql->limit(1);

        $value = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
        
        if ($value !== false) {
            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            return $unserialized !== false ? $unserialized : $value;
        }

        return $default;
    }

    /**
     * Set configuration value
     *
     * @param string $name
     * @param mixed $value
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function set($name, $value, $id_shop = null, $id_shop_group = null)
    {
        if ($id_shop === null) {
            $id_shop = Context::getContext()->shop->id;
        }
        
        if ($id_shop_group === null) {
            $id_shop_group = Context::getContext()->shop->id_shop_group;
        }

        // Serialize complex values
        if (is_array($value) || is_object($value)) {
            $value = serialize($value);
        }

        // Check if config already exists
        $sql = new DbQuery();
        $sql->select('id_config');
        $sql->from('st_review_config');
        $sql->where('name = "' . pSQL($name) . '"');
        $sql->where('id_shop = ' . (int)$id_shop);
        $sql->where('id_shop_group = ' . (int)$id_shop_group);

        $id_config = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if ($id_config) {
            // Update existing
            $config = new StReviewConfig($id_config);
            $config->value = $value;
            return $config->update();
        } else {
            // Create new
            $config = new StReviewConfig();
            $config->name = $name;
            $config->value = $value;
            $config->id_shop = $id_shop;
            $config->id_shop_group = $id_shop_group;
            return $config->add();
        }
    }

    /**
     * Delete configuration
     *
     * @param string $name
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function deleteConfig($name, $id_shop = null, $id_shop_group = null)
    {
        $where = 'name = "' . pSQL($name) . '"';
        
        if ($id_shop !== null) {
            $where .= ' AND id_shop = ' . (int)$id_shop;
        }
        
        if ($id_shop_group !== null) {
            $where .= ' AND id_shop_group = ' . (int)$id_shop_group;
        }

        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_config` WHERE ' . $where;
        return Db::getInstance()->execute($sql);
    }

    /**
     * Get all configurations
     *
     * @param int $id_shop
     * @param int $id_shop_group
     * @return array
     */
    public static function getAll($id_shop = null, $id_shop_group = null)
    {
        if ($id_shop === null) {
            $id_shop = Context::getContext()->shop->id;
        }
        
        if ($id_shop_group === null) {
            $id_shop_group = Context::getContext()->shop->id_shop_group;
        }

        $sql = new DbQuery();
        $sql->select('name, value');
        $sql->from('st_review_config');
        $sql->where('(id_shop = ' . (int)$id_shop . ' OR id_shop IS NULL)');
        $sql->where('(id_shop_group = ' . (int)$id_shop_group . ' OR id_shop_group IS NULL)');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        
        $configs = [];
        foreach ($results as $result) {
            $value = $result['value'];
            $unserialized = @unserialize($value);
            $configs[$result['name']] = $unserialized !== false ? $unserialized : $value;
        }

        return $configs;
    }

    /**
     * Get default configuration values
     *
     * @return array
     */
    public static function getDefaultConfig()
    {
        return [
            // General Settings
            'product_reviews_enabled' => true,
            'store_reviews_enabled' => true,
            'moderation_enabled' => true,
            'auto_approve_verified' => false,
            
            // Email Settings
            'email_enabled' => true,
            'email_delay_days' => 7,
            'reminder_enabled' => true,
            'reminder_delay_days' => 14,
            'email_order_statuses' => [2, 3, 4], // Paid, Preparation, Shipped
            
            // Display Settings
            'star_color' => '#FFD700',
            'star_size' => 20,
            'verified_badge_enabled' => true,
            'verified_badge_text' => 'Verified Purchase',
            'verified_badge_color' => '#28a745',
            
            // Social Sharing
            'social_sharing_enabled' => true,
            'facebook_sharing' => true,
            'twitter_sharing' => true,
            
            // Google My Business
            'google_my_business_enabled' => false,
            'google_my_business_place_id' => '',
            
            // Bad Review Management
            'bad_review_threshold' => 3.0,
            'bad_review_alerts_enabled' => true,
            'bad_review_alert_emails' => '',
            
            // Advanced Pack Compatibility
            'advanced_pack_enabled' => false,
            'pack_individual_reviews' => true,
            
            // Import/Export
            'import_batch_size' => 100,
            'export_include_ratings' => true,
            
            // CRON Settings
            'cron_enabled' => true,
            'cron_batch_size' => 50,
            'cron_frequency' => 'hourly',
            
            // Dispute Management
            'dispute_auto_notify' => true,
            'dispute_resolution_days' => 7,
            
            // Email Templates
            'email_templates' => [
                'product_request' => [
                    'subject' => 'Please review your recent purchase',
                    'content' => 'Hi {customer_firstname}, we hope you\'re enjoying your recent purchase. Please take a moment to share your experience.'
                ],
                'store_request' => [
                    'subject' => 'How was your shopping experience?',
                    'content' => 'Hi {customer_firstname}, we\'d love to hear about your shopping experience with us.'
                ],
                'reminder' => [
                    'subject' => 'Reminder: Share your experience',
                    'content' => 'Hi {customer_firstname}, this is a friendly reminder to share your experience with your recent purchase.'
                ]
            ]
        ];
    }

    /**
     * Initialize default configuration
     *
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function initializeDefaults($id_shop = null, $id_shop_group = null)
    {
        $defaults = self::getDefaultConfig();
        
        foreach ($defaults as $name => $value) {
            if (!self::configExists($name, $id_shop, $id_shop_group)) {
                if (!self::set($name, $value, $id_shop, $id_shop_group)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if configuration exists
     *
     * @param string $name
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function configExists($name, $id_shop = null, $id_shop_group = null)
    {
        if ($id_shop === null) {
            $id_shop = Context::getContext()->shop->id;
        }
        
        if ($id_shop_group === null) {
            $id_shop_group = Context::getContext()->shop->id_shop_group;
        }

        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_config');
        $sql->where('name = "' . pSQL($name) . '"');
        $sql->where('id_shop = ' . (int)$id_shop);
        $sql->where('id_shop_group = ' . (int)$id_shop_group);

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Export configuration
     *
     * @param int $id_shop
     * @param int $id_shop_group
     * @return string
     */
    public static function exportConfig($id_shop = null, $id_shop_group = null)
    {
        $configs = self::getAll($id_shop, $id_shop_group);
        return json_encode($configs, JSON_PRETTY_PRINT);
    }

    /**
     * Import configuration
     *
     * @param string $json_config
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function importConfig($json_config, $id_shop = null, $id_shop_group = null)
    {
        $configs = json_decode($json_config, true);
        
        if (!is_array($configs)) {
            return false;
        }

        foreach ($configs as $name => $value) {
            if (!self::set($name, $value, $id_shop, $id_shop_group)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Reset configuration to defaults
     *
     * @param int $id_shop
     * @param int $id_shop_group
     * @return bool
     */
    public static function resetToDefaults($id_shop = null, $id_shop_group = null)
    {
        // Delete existing configs
        $where = '1=1';
        
        if ($id_shop !== null) {
            $where .= ' AND id_shop = ' . (int)$id_shop;
        }
        
        if ($id_shop_group !== null) {
            $where .= ' AND id_shop_group = ' . (int)$id_shop_group;
        }

        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_review_config` WHERE ' . $where;
        
        if (!Db::getInstance()->execute($sql)) {
            return false;
        }

        // Initialize defaults
        return self::initializeDefaults($id_shop, $id_shop_group);
    }

    /**
     * Get configuration by category
     *
     * @param string $category
     * @param int $id_shop
     * @param int $id_shop_group
     * @return array
     */
    public static function getByCategory($category, $id_shop = null, $id_shop_group = null)
    {
        $all_configs = self::getAll($id_shop, $id_shop_group);
        $category_configs = [];

        $categories = [
            'general' => ['product_reviews_enabled', 'store_reviews_enabled', 'moderation_enabled', 'auto_approve_verified'],
            'email' => ['email_enabled', 'email_delay_days', 'reminder_enabled', 'reminder_delay_days', 'email_order_statuses'],
            'display' => ['star_color', 'star_size', 'verified_badge_enabled', 'verified_badge_text', 'verified_badge_color'],
            'social' => ['social_sharing_enabled', 'facebook_sharing', 'twitter_sharing'],
            'google' => ['google_my_business_enabled', 'google_my_business_place_id'],
            'alerts' => ['bad_review_threshold', 'bad_review_alerts_enabled', 'bad_review_alert_emails'],
            'advanced' => ['advanced_pack_enabled', 'pack_individual_reviews'],
            'cron' => ['cron_enabled', 'cron_batch_size', 'cron_frequency'],
            'disputes' => ['dispute_auto_notify', 'dispute_resolution_days']
        ];

        if (isset($categories[$category])) {
            foreach ($categories[$category] as $config_name) {
                if (isset($all_configs[$config_name])) {
                    $category_configs[$config_name] = $all_configs[$config_name];
                }
            }
        }

        return $category_configs;
    }

    /**
     * Validate configuration value
     *
     * @param string $name
     * @param mixed $value
     * @return bool
     */
    public static function validateConfig($name, $value)
    {
        $validations = [
            'email_delay_days' => function($v) { return is_numeric($v) && $v >= 0 && $v <= 365; },
            'reminder_delay_days' => function($v) { return is_numeric($v) && $v >= 0 && $v <= 365; },
            'star_size' => function($v) { return is_numeric($v) && $v >= 10 && $v <= 50; },
            'bad_review_threshold' => function($v) { return is_numeric($v) && $v >= 1 && $v <= 5; },
            'cron_batch_size' => function($v) { return is_numeric($v) && $v >= 1 && $v <= 1000; },
            'dispute_resolution_days' => function($v) { return is_numeric($v) && $v >= 1 && $v <= 30; },
        ];

        if (isset($validations[$name])) {
            return $validations[$name]($value);
        }

        return true; // No specific validation
    }
}
