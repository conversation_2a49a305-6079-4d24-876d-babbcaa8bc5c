<?php
/**
 * Review Dispute Class
 *
 * <AUTHOR> Code
 * @copyright 2024 Augment Code
 * @license   https://opensource.org/licenses/MIT MIT License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StReviewDispute extends ObjectModel
{
    /** @var int Review ID */
    public $id_review;

    /** @var int Customer ID */
    public $id_customer;

    /** @var string Reason */
    public $reason;

    /** @var string Description */
    public $description;

    /** @var string Status (open, in_progress, resolved, rejected) */
    public $status;

    /** @var string Resolution */
    public $resolution;

    /** @var string Date add */
    public $date_add;

    /** @var string Date update */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_review_disputes',
        'primary' => 'id_dispute',
        'fields' => [
            'id_review' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'reason' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'description' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml', 'required' => true],
            'status' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['open', 'in_progress', 'resolved', 'rejected']],
            'resolution' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get disputes by status
     *
     * @param string $status
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public static function getDisputesByStatus($status = 'open', $limit = 20, $offset = 0)
    {
        $sql = new DbQuery();
        $sql->select('d.*, r.title as review_title, r.content as review_content, r.overall_rating, c.firstname, c.lastname, c.email');
        $sql->from('st_review_disputes', 'd');
        $sql->innerJoin('st_reviews', 'r', 'd.id_review = r.id_review');
        $sql->leftJoin('customer', 'c', 'd.id_customer = c.id_customer');
        $sql->where('d.status = "' . pSQL($status) . '"');
        $sql->orderBy('d.date_add DESC');
        $sql->limit((int)$limit, (int)$offset);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get all disputes
     *
     * @param int $limit
     * @param int $offset
     * @param string $order_by
     * @param string $order_way
     * @return array
     */
    public static function getAllDisputes($limit = 20, $offset = 0, $order_by = 'date_add', $order_way = 'DESC')
    {
        $sql = new DbQuery();
        $sql->select('d.*, r.title as review_title, r.content as review_content, r.overall_rating, c.firstname, c.lastname, c.email');
        $sql->from('st_review_disputes', 'd');
        $sql->innerJoin('st_reviews', 'r', 'd.id_review = r.id_review');
        $sql->leftJoin('customer', 'c', 'd.id_customer = c.id_customer');
        $sql->orderBy(pSQL($order_by) . ' ' . pSQL($order_way));
        $sql->limit((int)$limit, (int)$offset);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get disputes by customer
     *
     * @param int $id_customer
     * @return array
     */
    public static function getDisputesByCustomer($id_customer)
    {
        $sql = new DbQuery();
        $sql->select('d.*, r.title as review_title, r.content as review_content, r.overall_rating');
        $sql->from('st_review_disputes', 'd');
        $sql->innerJoin('st_reviews', 'r', 'd.id_review = r.id_review');
        $sql->where('d.id_customer = ' . (int)$id_customer);
        $sql->orderBy('d.date_add DESC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get dispute by review
     *
     * @param int $id_review
     * @return array|false
     */
    public static function getDisputeByReview($id_review)
    {
        $sql = new DbQuery();
        $sql->select('d.*, c.firstname, c.lastname, c.email');
        $sql->from('st_review_disputes', 'd');
        $sql->leftJoin('customer', 'c', 'd.id_customer = c.id_customer');
        $sql->where('d.id_review = ' . (int)$id_review);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Check if review has dispute
     *
     * @param int $id_review
     * @return bool
     */
    public static function hasDispute($id_review)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('st_review_disputes');
        $sql->where('id_review = ' . (int)$id_review);

        return (bool)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get dispute statistics
     *
     * @param int $days
     * @return array
     */
    public static function getDisputeStats($days = 30)
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_disputes,
            SUM(CASE WHEN status = "open" THEN 1 ELSE 0 END) as open_disputes,
            SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as in_progress_disputes,
            SUM(CASE WHEN status = "resolved" THEN 1 ELSE 0 END) as resolved_disputes,
            SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_disputes,
            AVG(TIMESTAMPDIFF(DAY, date_add, COALESCE(date_upd, NOW()))) as avg_resolution_days
        ');
        $sql->from('st_review_disputes');
        $sql->where('date_add >= DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Get dispute reasons
     *
     * @return array
     */
    public static function getDisputeReasons()
    {
        return [
            'inappropriate_content' => 'Inappropriate Content',
            'fake_review' => 'Fake Review',
            'spam' => 'Spam',
            'personal_information' => 'Contains Personal Information',
            'off_topic' => 'Off Topic',
            'duplicate' => 'Duplicate Review',
            'other' => 'Other'
        ];
    }

    /**
     * Update dispute status
     *
     * @param string $status
     * @param string $resolution
     * @return bool
     */
    public function updateStatus($status, $resolution = null)
    {
        $this->status = $status;
        if ($resolution) {
            $this->resolution = $resolution;
        }
        return $this->update();
    }

    /**
     * Resolve dispute
     *
     * @param string $resolution
     * @param bool $approve_review
     * @return bool
     */
    public function resolve($resolution, $approve_review = true)
    {
        $this->status = 'resolved';
        $this->resolution = $resolution;

        if ($this->update()) {
            // Update review status if needed
            if ($approve_review) {
                $review = new StReview($this->id_review);
                if (Validate::isLoadedObject($review)) {
                    $review->approve();
                }
            }

            // Send notification to customer
            $this->sendResolutionNotification();

            return true;
        }

        return false;
    }

    /**
     * Reject dispute
     *
     * @param string $reason
     * @return bool
     */
    public function reject($reason)
    {
        $this->status = 'rejected';
        $this->resolution = $reason;

        if ($this->update()) {
            // Send notification to customer
            $this->sendRejectionNotification();
            return true;
        }

        return false;
    }

    /**
     * Send resolution notification to customer
     *
     * @return bool
     */
    public function sendResolutionNotification()
    {
        $customer = new Customer($this->id_customer);
        if (!Validate::isLoadedObject($customer)) {
            return false;
        }

        $review = new StReview($this->id_review);
        if (!Validate::isLoadedObject($review)) {
            return false;
        }

        $template_vars = [
            '{customer_firstname}' => $customer->firstname,
            '{customer_lastname}' => $customer->lastname,
            '{dispute_reason}' => $this->reason,
            '{dispute_description}' => $this->description,
            '{resolution}' => $this->resolution,
            '{review_title}' => $review->title,
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{shop_url}' => Tools::getShopDomainSsl(true),
        ];

        try {
            Mail::Send(
                (int)Context::getContext()->language->id,
                'dispute_resolved',
                Mail::l('Your dispute has been resolved'),
                $template_vars,
                $customer->email,
                $customer->firstname . ' ' . $customer->lastname,
                null,
                null,
                null,
                null,
                dirname(__FILE__) . '/../mails/',
                false,
                (int)Context::getContext()->shop->id
            );
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Send rejection notification to customer
     *
     * @return bool
     */
    public function sendRejectionNotification()
    {
        $customer = new Customer($this->id_customer);
        if (!Validate::isLoadedObject($customer)) {
            return false;
        }

        $review = new StReview($this->id_review);
        if (!Validate::isLoadedObject($review)) {
            return false;
        }

        $template_vars = [
            '{customer_firstname}' => $customer->firstname,
            '{customer_lastname}' => $customer->lastname,
            '{dispute_reason}' => $this->reason,
            '{dispute_description}' => $this->description,
            '{rejection_reason}' => $this->resolution,
            '{review_title}' => $review->title,
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{shop_url}' => Tools::getShopDomainSsl(true),
        ];

        try {
            Mail::Send(
                (int)Context::getContext()->language->id,
                'dispute_rejected',
                Mail::l('Your dispute has been reviewed'),
                $template_vars,
                $customer->email,
                $customer->firstname . ' ' . $customer->lastname,
                null,
                null,
                null,
                null,
                dirname(__FILE__) . '/../mails/',
                false,
                (int)Context::getContext()->shop->id
            );
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get overdue disputes
     *
     * @param int $days
     * @return array
     */
    public static function getOverdueDisputes($days = 7)
    {
        $sql = new DbQuery();
        $sql->select('d.*, r.title as review_title, c.firstname, c.lastname, c.email');
        $sql->from('st_review_disputes', 'd');
        $sql->innerJoin('st_reviews', 'r', 'd.id_review = r.id_review');
        $sql->leftJoin('customer', 'c', 'd.id_customer = c.id_customer');
        $sql->where('d.status IN ("open", "in_progress")');
        $sql->where('d.date_add <= DATE_SUB(NOW(), INTERVAL ' . (int)$days . ' DAY)');
        $sql->orderBy('d.date_add ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }
}
